#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بناء ملف تنفيذي للتطبيق
Build Executable for the Application

معهد المتوسط للمهن الشاملة - اجخرة
"""

import os
import sys
import shutil
from pathlib import Path

def check_pyinstaller():
    """التحقق من وجود PyInstaller"""
    try:
        import PyInstaller
        print("✓ PyInstaller متوفر")
        return True
    except ImportError:
        print("✗ PyInstaller غير متوفر")
        print("لتثبيت PyInstaller، استخدم الأمر:")
        print("pip install pyinstaller")
        return False

def create_spec_file():
    """إنشاء ملف المواصفات لـ PyInstaller"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config', 'config'),
        ('database', 'database'),
        ('gui', 'gui'),
        ('utils', 'utils'),
        ('resources', 'resources'),
        ('docs', 'docs'),
        ('README.md', '.'),
        ('requirements.txt', '.'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'sqlite3',
        'datetime',
        'json',
        'csv',
        'shutil',
        'threading',
        'hashlib',
        're',
        'platform',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='StudentManagementSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='resources/icons/app_icon.ico' if os.path.exists('resources/icons/app_icon.ico') else None,
    version_file=None,
)
'''
    
    with open('student_management.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ تم إنشاء ملف المواصفات")

def build_executable():
    """بناء الملف التنفيذي"""
    print("بدء بناء الملف التنفيذي...")
    
    # إنشاء ملف المواصفات
    create_spec_file()
    
    # تشغيل PyInstaller
    import subprocess
    
    try:
        result = subprocess.run([
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            'student_management.spec'
        ], capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✓ تم بناء الملف التنفيذي بنجاح")
            
            # نسخ الملفات الإضافية
            copy_additional_files()
            
            print("\n" + "="*50)
            print("تم إنشاء الملف التنفيذي بنجاح!")
            print("الملف موجود في: dist/StudentManagementSystem.exe")
            print("="*50)
            
            return True
        else:
            print("✗ فشل في بناء الملف التنفيذي")
            print("الأخطاء:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ خطأ في بناء الملف التنفيذي: {e}")
        return False

def copy_additional_files():
    """نسخ الملفات الإضافية"""
    try:
        dist_dir = Path('dist')
        
        # إنشاء مجلد البيانات
        data_dir = dist_dir / 'data'
        data_dir.mkdir(exist_ok=True)
        
        # إنشاء مجلد النسخ الاحتياطية
        backup_dir = data_dir / 'backups'
        backup_dir.mkdir(exist_ok=True)
        
        # نسخ ملف التشغيل
        if os.path.exists('run_app.bat'):
            shutil.copy2('run_app.bat', dist_dir / 'run_app.bat')
        
        # إنشاء ملف تشغيل جديد للملف التنفيذي
        exe_bat_content = '''@echo off
chcp 65001 > nul
title نظام إدارة الطلاب - معهد المتوسط للمهن الشاملة

echo ================================================
echo نظام إدارة الطلاب
echo معهد المتوسط للمهن الشاملة - اجخرة
echo الإصدار 2.0
echo ================================================
echo.

echo جاري تشغيل النظام...
echo.

StudentManagementSystem.exe

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo حدث خطأ في تشغيل النظام
    echo.
    pause
)

echo.
echo تم إنهاء النظام
pause
'''
        
        with open(dist_dir / 'تشغيل_النظام.bat', 'w', encoding='utf-8') as f:
            f.write(exe_bat_content)
        
        # نسخ دليل المستخدم
        if os.path.exists('docs/user_guide.md'):
            docs_dir = dist_dir / 'docs'
            docs_dir.mkdir(exist_ok=True)
            shutil.copy2('docs/user_guide.md', docs_dir / 'user_guide.md')
        
        print("✓ تم نسخ الملفات الإضافية")
        
    except Exception as e:
        print(f"⚠️ تحذير: خطأ في نسخ الملفات الإضافية: {e}")

def create_installer_script():
    """إنشاء سكريبت تثبيت"""
    installer_content = '''@echo off
chcp 65001 > nul
title تثبيت نظام إدارة الطلاب

echo ================================================
echo تثبيت نظام إدارة الطلاب
echo معهد المتوسط للمهن الشاملة - اجخرة
echo ================================================
echo.

echo جاري تثبيت النظام...
echo.

REM إنشاء مجلد التطبيق
if not exist "C:\\Program Files\\StudentManagement" (
    mkdir "C:\\Program Files\\StudentManagement"
)

REM نسخ الملفات
xcopy /E /I /Y "." "C:\\Program Files\\StudentManagement\\"

REM إنشاء اختصار على سطح المكتب
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%USERPROFILE%\\Desktop\\نظام إدارة الطلاب.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "C:\\Program Files\\StudentManagement\\StudentManagementSystem.exe" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "C:\\Program Files\\StudentManagement" >> CreateShortcut.vbs
echo oLink.Description = "نظام إدارة الطلاب - معهد المتوسط للمهن الشاملة" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs
cscript CreateShortcut.vbs
del CreateShortcut.vbs

echo.
echo تم تثبيت النظام بنجاح!
echo يمكنك تشغيله من سطح المكتب أو من:
echo C:\\Program Files\\StudentManagement\\StudentManagementSystem.exe
echo.
pause
'''
    
    with open('dist/installer.bat', 'w', encoding='utf-8') as f:
        f.write(installer_content)
    
    print("✓ تم إنشاء سكريبت التثبيت")

def clean_build_files():
    """تنظيف ملفات البناء"""
    try:
        # حذف مجلدات البناء المؤقتة
        if os.path.exists('build'):
            shutil.rmtree('build')
        
        # حذف ملف المواصفات
        if os.path.exists('student_management.spec'):
            os.remove('student_management.spec')
        
        print("✓ تم تنظيف ملفات البناء المؤقتة")
        
    except Exception as e:
        print(f"⚠️ تحذير: خطأ في تنظيف ملفات البناء: {e}")

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("بناء ملف تنفيذي لنظام إدارة الطلاب")
    print("معهد المتوسط للمهن الشاملة - اجخرة")
    print("=" * 60)
    
    # التحقق من PyInstaller
    if not check_pyinstaller():
        return
    
    # التحقق من وجود الملفات المطلوبة
    if not os.path.exists('main.py'):
        print("✗ ملف main.py غير موجود")
        return
    
    # بناء الملف التنفيذي
    if build_executable():
        # إنشاء سكريبت التثبيت
        create_installer_script()
        
        # تنظيف ملفات البناء
        clean_build_files()
        
        print("\n" + "=" * 60)
        print("تم إنشاء الملف التنفيذي بنجاح!")
        print("\nالملفات المُنشأة:")
        print("- dist/StudentManagementSystem.exe (الملف التنفيذي)")
        print("- dist/تشغيل_النظام.bat (ملف التشغيل)")
        print("- dist/installer.bat (سكريبت التثبيت)")
        print("\nلتشغيل التطبيق:")
        print("1. انقر نقراً مزدوجاً على StudentManagementSystem.exe")
        print("2. أو استخدم ملف تشغيل_النظام.bat")
        print("\nللتثبيت على الكمبيوتر:")
        print("شغّل installer.bat كمدير")
        print("=" * 60)
    else:
        print("\n✗ فشل في إنشاء الملف التنفيذي")

if __name__ == "__main__":
    main()
