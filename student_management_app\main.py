#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الطلاب - الملف الرئيسي
Student Management System - Main Application

معهد المتوسط للمهن الشاملة - اجخرة
Institute of Comprehensive Vocational Skills - Ajkhara

الإصدار: 2.0
Version: 2.0
"""

import tkinter as tk
from tkinter import messagebox, ttk
import sys
import os
from datetime import datetime
import platform

# إضافة مسار المكتبات
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from database.db_manager import DatabaseManager
    from gui.main_window import MainWindow
    from utils.arabic_support import setup_arabic_font
    from config.settings import AppSettings
except ImportError as e:
    print(f"خطأ في استيراد المكتبات: {e}")
    print("تأكد من وجود جميع ملفات النظام في المجلدات الصحيحة")
    input("اضغط Enter للخروج...")
    sys.exit(1)

class StudentManagementApp:
    """
    التطبيق الرئيسي لنظام إدارة الطلاب
    Main Student Management Application
    """
    
    def __init__(self):
        """تهيئة التطبيق"""
        self.root = tk.Tk()
        self.db_manager = None
        self.main_window = None
        self.settings = AppSettings()
        
        # إعداد التطبيق
        self.setup_app()
    
    def setup_app(self):
        """إعداد التطبيق الرئيسي"""
        try:
            print("بدء تهيئة نظام إدارة الطلاب...")
            
            # إعداد النافذة الرئيسية
            self.setup_main_window()
            
            # إعداد الخط العربي
            setup_arabic_font(self.root)
            
            # إعداد قاعدة البيانات
            self.setup_database()
            
            # إنشاء النافذة الرئيسية
            self.main_window = MainWindow(self.root, self.db_manager, self.settings)
            
            # ربط إغلاق النافذة
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            
            print("تم إعداد التطبيق بنجاح")
            
        except Exception as e:
            error_msg = f"خطأ في إعداد التطبيق: {str(e)}"
            print(error_msg)
            messagebox.showerror("خطأ في التهيئة", error_msg)
            sys.exit(1)
    
    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        # عنوان النافذة
        self.root.title("نظام إدارة الطلاب - معهد المتوسط للمهن الشاملة - اجخرة")
        
        # حجم النافذة
        window_width = 1400
        window_height = 900
        self.root.geometry(f"{window_width}x{window_height}")
        
        # الحد الأدنى لحجم النافذة
        self.root.minsize(1200, 800)
        
        # لون الخلفية
        self.root.configure(bg='#ecf0f1')
        
        # توسيط النافذة
        self.center_window(window_width, window_height)
        
        # إعداد أيقونة التطبيق (إذا كانت متوفرة)
        self.setup_app_icon()
        
        # إعداد نمط النوافذ
        self.setup_window_style()
    
    def center_window(self, width, height):
        """توسيط النافذة على الشاشة"""
        # الحصول على أبعاد الشاشة
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # حساب موقع النافذة
        x = (screen_width // 2) - (width // 2)
        y = (screen_height // 2) - (height // 2)
        
        # تطبيق الموقع
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def setup_app_icon(self):
        """إعداد أيقونة التطبيق"""
        try:
            icon_path = os.path.join(current_dir, "resources", "icons", "app_icon.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except Exception as e:
            print(f"تعذر تحميل أيقونة التطبيق: {e}")
    
    def setup_window_style(self):
        """إعداد نمط النوافذ"""
        try:
            # إعداد نمط ttk
            style = ttk.Style()
            
            # اختيار نمط مناسب حسب نظام التشغيل
            if platform.system() == "Windows":
                style.theme_use('winnative')
            elif platform.system() == "Darwin":  # macOS
                style.theme_use('aqua')
            else:  # Linux
                style.theme_use('clam')
                
        except Exception as e:
            print(f"تعذر إعداد نمط النوافذ: {e}")
    
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            print("إعداد قاعدة البيانات...")
            
            # إنشاء مجلد البيانات إذا لم يكن موجوداً
            data_dir = os.path.join(current_dir, "data")
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
                print("تم إنشاء مجلد البيانات")
            
            # مسار قاعدة البيانات
            db_path = os.path.join(data_dir, "student_data.db")
            
            # إنشاء مدير قاعدة البيانات
            self.db_manager = DatabaseManager(db_path)
            
            print("تم إعداد قاعدة البيانات بنجاح")
            
        except Exception as e:
            error_msg = f"خطأ في إعداد قاعدة البيانات: {str(e)}"
            print(error_msg)
            messagebox.showerror("خطأ في قاعدة البيانات", error_msg)
            raise
    
    def on_closing(self):
        """معالجة إغلاق التطبيق"""
        try:
            # حفظ الإعدادات
            self.settings.save_settings()
            
            # إغلاق قاعدة البيانات
            if self.db_manager:
                self.db_manager.close_connection()
            
            print("تم إغلاق التطبيق بنجاح")
            self.root.destroy()
            
        except Exception as e:
            print(f"خطأ في إغلاق التطبيق: {e}")
            self.root.destroy()
    
    def run(self):
        """تشغيل التطبيق"""
        try:
            print("=" * 60)
            print("نظام إدارة الطلاب")
            print("معهد المتوسط للمهن الشاملة - اجخرة")
            print("الإصدار 2.0")
            print("=" * 60)
            print("بدء تشغيل التطبيق...")
            
            # تشغيل الحلقة الرئيسية
            self.root.mainloop()
            
        except Exception as e:
            error_msg = f"خطأ في تشغيل التطبيق: {str(e)}"
            print(error_msg)
            messagebox.showerror("خطأ في التشغيل", error_msg)
        finally:
            print("تم إنهاء التطبيق")

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 6):
        print("خطأ: يتطلب هذا البرنامج Python 3.6 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        input("اضغط Enter للخروج...")
        return False
    return True

def check_dependencies():
    """التحقق من المكتبات المطلوبة"""
    required_modules = ['tkinter', 'sqlite3', 'datetime', 'platform']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"خطأ: المكتبات التالية غير متوفرة: {', '.join(missing_modules)}")
        input("اضغط Enter للخروج...")
        return False
    
    return True

def main():
    """الدالة الرئيسية"""
    try:
        # التحقق من المتطلبات
        if not check_python_version():
            return
        
        if not check_dependencies():
            return
        
        # إنشاء وتشغيل التطبيق
        app = StudentManagementApp()
        app.run()
        
    except KeyboardInterrupt:
        print("\nتم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        error_msg = f"خطأ عام في البرنامج: {str(e)}"
        print(error_msg)
        try:
            messagebox.showerror("خطأ", error_msg)
        except:
            pass

if __name__ == "__main__":
    main()
