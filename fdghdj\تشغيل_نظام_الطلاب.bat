@echo off
chcp 65001 >nul
title Student Management System
cls
echo ================================================
echo Student Management System
echo Institute of Comprehensive Vocational Skills
echo ================================================
echo.
echo Starting the system...
echo.

REM Check Python installation
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    echo.
    pause
    exit /b 1
)

REM Check main file exists
if not exist "run_student_system.py" (
    echo Error: run_student_system.py file not found
    echo Make sure all program files are present
    echo.
    pause
    exit /b 1
)

REM Run the program
echo Python found, starting the system...
echo.
python run_student_system.py

REM Check exit status
if %errorlevel% neq 0 (
    echo.
    echo ================================================
    echo Error occurred while running the system
    echo ================================================
    echo.
    echo Possible causes:
    echo 1. Program files missing or corrupted
    echo 2. Permission issues
    echo 3. Python configuration problems
    echo.
    echo Suggested solutions:
    echo 1. Make sure all program files exist
    echo 2. Run as administrator
    echo 3. Reinstall Python
    echo.
    pause
) else (
    echo.
    echo Program ended successfully
)
