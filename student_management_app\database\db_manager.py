#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات - نظام إدارة الطلاب
Database Manager for Student Management System

معهد المتوسط للمهن الشاملة - اجخرة
الإصدار المحسن 2.0
"""

import sqlite3
import os
import shutil
from datetime import datetime, timedelta
import hashlib
import json
import threading

class DatabaseManager:
    """مدير قاعدة البيانات المحسن"""
    
    def __init__(self, db_path="student_data.db"):
        """تهيئة مدير قاعدة البيانات"""
        self.db_path = db_path
        self.connection = None
        self.lock = threading.Lock()
        
        # إنشاء قاعدة البيانات والجداول
        self.init_database()
        
        # إعداد النسخ الاحتياطي التلقائي
        self.setup_auto_backup()
    
    def get_connection(self):
        """الحصول على اتصال آمن بقاعدة البيانات"""
        with self.lock:
            if self.connection is None:
                self.connection = sqlite3.connect(
                    self.db_path, 
                    check_same_thread=False,
                    timeout=30.0
                )
                self.connection.row_factory = sqlite3.Row
            return self.connection
    
    def close_connection(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        with self.lock:
            if self.connection:
                self.connection.close()
                self.connection = None
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # تفعيل المفاتيح الخارجية
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # جدول الطلاب المحسن
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS students (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                full_name TEXT NOT NULL,
                national_id TEXT UNIQUE NOT NULL,
                seat_number TEXT UNIQUE NOT NULL,
                section TEXT NOT NULL,
                birth_date TEXT,
                gender TEXT CHECK(gender IN ('ذكر', 'أنثى')),
                phone TEXT,
                address TEXT,
                guardian_name TEXT,
                guardian_phone TEXT,
                enrollment_date TEXT DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'نشط' CHECK(status IN ('نشط', 'منقطع', 'متخرج', 'محول')),
                notes TEXT,
                created_date TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_date TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الشعب المحسن
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                section_name TEXT UNIQUE NOT NULL,
                section_code TEXT UNIQUE,
                capacity INTEGER DEFAULT 30,
                current_count INTEGER DEFAULT 0,
                academic_year TEXT,
                status TEXT DEFAULT 'نشط' CHECK(status IN ('نشط', 'مغلق', 'مؤجل')),
                created_date TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المواد المحسن
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS subjects (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                subject_name TEXT NOT NULL,
                subject_code TEXT UNIQUE,
                credit_hours INTEGER DEFAULT 3,
                max_grade REAL DEFAULT 100,
                passing_grade REAL DEFAULT 50,
                subject_type TEXT DEFAULT 'نظري' CHECK(subject_type IN ('نظري', 'عملي', 'مختلط')),
                semester TEXT DEFAULT 'سنوي' CHECK(semester IN ('الأول', 'الثاني', 'سنوي')),
                status TEXT DEFAULT 'نشط' CHECK(status IN ('نشط', 'ملغي')),
                created_date TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول ربط الشعب بالمواد
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS section_subjects (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                section_id INTEGER NOT NULL,
                subject_id INTEGER NOT NULL,
                academic_year TEXT NOT NULL,
                teacher_name TEXT,
                FOREIGN KEY (section_id) REFERENCES sections (id) ON DELETE CASCADE,
                FOREIGN KEY (subject_id) REFERENCES subjects (id) ON DELETE CASCADE,
                UNIQUE(section_id, subject_id, academic_year)
            )
        ''')
        
        # جدول الدرجات المحسن
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS grades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_id INTEGER NOT NULL,
                subject_id INTEGER NOT NULL,
                academic_year TEXT NOT NULL,
                semester1_work REAL DEFAULT 0 CHECK(semester1_work >= 0),
                semester1_exam REAL DEFAULT 0 CHECK(semester1_exam >= 0),
                semester2_work REAL DEFAULT 0 CHECK(semester2_work >= 0),
                semester2_exam REAL DEFAULT 0 CHECK(semester2_exam >= 0),
                final_grade REAL GENERATED ALWAYS AS (
                    semester1_work + semester1_exam + semester2_work + semester2_exam
                ) STORED,
                second_round_exam REAL DEFAULT 0 CHECK(second_round_exam >= 0),
                final_grade_after_second_round REAL,
                is_passed BOOLEAN DEFAULT 0,
                passed_in_second_round BOOLEAN DEFAULT 0,
                grade_letter TEXT,
                grade_points REAL,
                entry_date TEXT DEFAULT CURRENT_TIMESTAMP,
                last_updated TEXT DEFAULT CURRENT_TIMESTAMP,
                entered_by TEXT,
                FOREIGN KEY (student_id) REFERENCES students (id) ON DELETE CASCADE,
                FOREIGN KEY (subject_id) REFERENCES subjects (id) ON DELETE CASCADE,
                UNIQUE(student_id, subject_id, academic_year)
            )
        ''')
        
        # جدول النسخ الاحتياطية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS backups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                backup_name TEXT NOT NULL,
                backup_path TEXT NOT NULL,
                backup_size INTEGER,
                backup_date TEXT DEFAULT CURRENT_TIMESTAMP,
                backup_type TEXT DEFAULT 'يدوي' CHECK(backup_type IN ('يدوي', 'تلقائي')),
                description TEXT
            )
        ''')
        
        # جدول سجل العمليات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS activity_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                action_type TEXT NOT NULL,
                table_name TEXT,
                record_id INTEGER,
                old_values TEXT,
                new_values TEXT,
                user_name TEXT,
                timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                ip_address TEXT
            )
        ''')
        
        # إنشاء الفهارس لتحسين الأداء
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_students_section ON students(section)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_students_national_id ON students(national_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_students_seat_number ON students(seat_number)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_grades_student_id ON grades(student_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_grades_subject_id ON grades(subject_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_grades_academic_year ON grades(academic_year)')
        
        # إنشاء المشاهد (Views) للاستعلامات المعقدة
        cursor.execute('''
            CREATE VIEW IF NOT EXISTS student_grades_view AS
            SELECT 
                s.id as student_id,
                s.full_name,
                s.national_id,
                s.seat_number,
                s.section,
                sub.subject_name,
                g.academic_year,
                g.semester1_work,
                g.semester1_exam,
                g.semester2_work,
                g.semester2_exam,
                g.final_grade,
                g.second_round_exam,
                g.final_grade_after_second_round,
                g.is_passed,
                g.passed_in_second_round,
                g.grade_letter
            FROM students s
            LEFT JOIN grades g ON s.id = g.student_id
            LEFT JOIN subjects sub ON g.subject_id = sub.id
        ''')
        
        conn.commit()
        conn.close()
        
        # إضافة بيانات افتراضية
        self.add_default_data()
    
    def add_default_data(self):
        """إضافة بيانات افتراضية للنظام"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # إضافة شعب افتراضية
            default_sections = [
                ("الشعبة الأولى", "SEC001", 30),
                ("الشعبة الثانية", "SEC002", 30),
                ("الشعبة الثالثة", "SEC003", 30),
                ("شعبة الحاسوب", "COMP01", 25),
                ("شعبة المحاسبة", "ACC01", 25),
                ("شعبة التمريض", "NURS01", 20)
            ]
            
            current_year = datetime.now().strftime("%Y-%Y")
            
            for section_name, section_code, capacity in default_sections:
                cursor.execute('''
                    INSERT OR IGNORE INTO sections 
                    (section_name, section_code, capacity, academic_year) 
                    VALUES (?, ?, ?, ?)
                ''', (section_name, section_code, capacity, current_year))
            
            # إضافة مواد افتراضية
            default_subjects = [
                ("الرياضيات", "MATH101", 4, 100, 50, "نظري", "سنوي"),
                ("الفيزياء", "PHYS101", 3, 100, 50, "مختلط", "سنوي"),
                ("الكيمياء", "CHEM101", 3, 100, 50, "مختلط", "سنوي"),
                ("اللغة العربية", "ARAB101", 3, 100, 50, "نظري", "سنوي"),
                ("اللغة الإنجليزية", "ENG101", 3, 100, 50, "نظري", "سنوي"),
                ("الحاسوب", "COMP101", 4, 100, 50, "عملي", "سنوي"),
                ("التقنية المهنية", "TECH101", 5, 100, 50, "عملي", "سنوي"),
                ("المحاسبة", "ACC101", 4, 100, 50, "نظري", "سنوي"),
                ("التمريض", "NURS101", 5, 100, 50, "عملي", "سنوي"),
                ("علم النفس", "PSYC101", 2, 100, 50, "نظري", "الأول"),
                ("الإحصاء", "STAT101", 3, 100, 50, "نظري", "الثاني")
            ]
            
            for subject_data in default_subjects:
                cursor.execute('''
                    INSERT OR IGNORE INTO subjects 
                    (subject_name, subject_code, credit_hours, max_grade, 
                     passing_grade, subject_type, semester) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', subject_data)
            
            conn.commit()
            print("تم إضافة البيانات الافتراضية بنجاح")
            
        except Exception as e:
            print(f"خطأ في إضافة البيانات الافتراضية: {e}")
            conn.rollback()
        finally:
            conn.close()
    
    def setup_auto_backup(self):
        """إعداد النسخ الاحتياطي التلقائي"""
        try:
            backup_dir = os.path.join(os.path.dirname(self.db_path), "backups")
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)
        except Exception as e:
            print(f"خطأ في إعداد مجلد النسخ الاحتياطي: {e}")
    
    def create_backup(self, backup_name=None, description=""):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            if backup_name is None:
                backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            
            backup_dir = os.path.join(os.path.dirname(self.db_path), "backups")
            backup_path = os.path.join(backup_dir, backup_name)
            
            # نسخ قاعدة البيانات
            shutil.copy2(self.db_path, backup_path)
            
            # تسجيل النسخة الاحتياطية
            backup_size = os.path.getsize(backup_path)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO backups (backup_name, backup_path, backup_size, description)
                VALUES (?, ?, ?, ?)
            ''', (backup_name, backup_path, backup_size, description))
            
            conn.commit()
            conn.close()
            
            return True, backup_path
            
        except Exception as e:
            return False, str(e)
    
    def log_activity(self, action_type, table_name=None, record_id=None, 
                    old_values=None, new_values=None, user_name="النظام"):
        """تسجيل العمليات في سجل النشاط"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO activity_log 
                (action_type, table_name, record_id, old_values, new_values, user_name)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                action_type, table_name, record_id,
                json.dumps(old_values, ensure_ascii=False) if old_values else None,
                json.dumps(new_values, ensure_ascii=False) if new_values else None,
                user_name
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في تسجيل النشاط: {e}")
    
    def generate_seat_number(self, full_name, section):
        """توليد رقم الجلوس المحسن"""
        try:
            # الحصول على كود الشعبة
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT section_code, current_count 
                FROM sections 
                WHERE section_name = ?
            ''', (section,))
            
            result = cursor.fetchone()
            if result:
                section_code, current_count = result
                next_number = current_count + 1
                
                # تحديث عدد الطلاب في الشعبة
                cursor.execute('''
                    UPDATE sections 
                    SET current_count = current_count + 1 
                    WHERE section_name = ?
                ''', (section,))
                
                conn.commit()
                conn.close()
                
                # تكوين رقم الجلوس: كود الشعبة + رقم تسلسلي
                seat_number = f"{section_code}{next_number:03d}"
                return seat_number
            else:
                conn.close()
                # إذا لم توجد الشعبة، استخدم الطريقة القديمة
                return self._generate_legacy_seat_number(full_name, section)
                
        except Exception as e:
            print(f"خطأ في توليد رقم الجلوس: {e}")
            return self._generate_legacy_seat_number(full_name, section)
    
    def _generate_legacy_seat_number(self, full_name, section):
        """الطريقة القديمة لتوليد رقم الجلوس"""
        name_hash = hashlib.md5(f"{full_name}{section}".encode('utf-8')).hexdigest()[:6]
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM students WHERE section = ?", (section,))
        count = cursor.fetchone()[0]
        conn.close()
        
        seat_number = f"{section[:2]}{count + 1:03d}{name_hash[:3].upper()}"
        return seat_number

    def add_student(self, student_data):
        """إضافة طالب جديد"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # توليد رقم الجلوس
            seat_number = self.generate_seat_number(
                student_data['full_name'],
                student_data['section']
            )

            cursor.execute('''
                INSERT INTO students (
                    full_name, national_id, seat_number, section,
                    birth_date, gender, phone, address,
                    guardian_name, guardian_phone, notes
                )
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                student_data['full_name'],
                student_data['national_id'],
                seat_number,
                student_data['section'],
                student_data.get('birth_date', ''),
                student_data.get('gender', ''),
                student_data.get('phone', ''),
                student_data.get('address', ''),
                student_data.get('guardian_name', ''),
                student_data.get('guardian_phone', ''),
                student_data.get('notes', '')
            ))

            student_id = cursor.lastrowid
            conn.commit()
            conn.close()

            # تسجيل العملية
            self.log_activity(
                "إضافة طالب",
                "students",
                student_id,
                None,
                student_data
            )

            return student_id, seat_number

        except sqlite3.IntegrityError as e:
            if "national_id" in str(e):
                raise Exception("الرقم الوطني موجود مسبقاً")
            elif "seat_number" in str(e):
                raise Exception("رقم الجلوس موجود مسبقاً")
            else:
                raise Exception(f"خطأ في البيانات: {str(e)}")
        except Exception as e:
            raise Exception(f"خطأ في إضافة الطالب: {str(e)}")

    def update_student(self, student_id, student_data):
        """تحديث بيانات طالب"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # الحصول على البيانات القديمة
            cursor.execute("SELECT * FROM students WHERE id = ?", (student_id,))
            old_data = dict(cursor.fetchone()) if cursor.fetchone() else None

            cursor.execute('''
                UPDATE students SET
                    full_name = ?, national_id = ?, section = ?,
                    birth_date = ?, gender = ?, phone = ?,
                    address = ?, guardian_name = ?, guardian_phone = ?,
                    notes = ?, updated_date = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (
                student_data['full_name'],
                student_data['national_id'],
                student_data['section'],
                student_data.get('birth_date', ''),
                student_data.get('gender', ''),
                student_data.get('phone', ''),
                student_data.get('address', ''),
                student_data.get('guardian_name', ''),
                student_data.get('guardian_phone', ''),
                student_data.get('notes', ''),
                student_id
            ))

            conn.commit()
            conn.close()

            # تسجيل العملية
            self.log_activity(
                "تحديث طالب",
                "students",
                student_id,
                old_data,
                student_data
            )

            return True

        except Exception as e:
            raise Exception(f"خطأ في تحديث الطالب: {str(e)}")

    def delete_student(self, student_id):
        """حذف طالب"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # الحصول على بيانات الطالب قبل الحذف
            cursor.execute("SELECT * FROM students WHERE id = ?", (student_id,))
            student_data = dict(cursor.fetchone()) if cursor.fetchone() else None

            if not student_data:
                raise Exception("الطالب غير موجود")

            # حذف الطالب (سيتم حذف الدرجات تلقائياً بسبب CASCADE)
            cursor.execute("DELETE FROM students WHERE id = ?", (student_id,))

            conn.commit()
            conn.close()

            # تسجيل العملية
            self.log_activity(
                "حذف طالب",
                "students",
                student_id,
                student_data,
                None
            )

            return True

        except Exception as e:
            raise Exception(f"خطأ في حذف الطالب: {str(e)}")

    def get_student_by_id(self, student_id):
        """الحصول على بيانات طالب بالمعرف"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT * FROM students WHERE id = ?", (student_id,))
            result = cursor.fetchone()
            conn.close()

            return result

        except Exception as e:
            print(f"خطأ في الحصول على بيانات الطالب: {e}")
            return None

    def get_students_by_section(self, section):
        """الحصول على طلاب شعبة معينة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM students
                WHERE section = ? AND status = 'نشط'
                ORDER BY seat_number
            ''', (section,))

            results = cursor.fetchall()
            conn.close()

            return results

        except Exception as e:
            print(f"خطأ في الحصول على طلاب الشعبة: {e}")
            return []

    def search_student(self, search_term):
        """البحث عن طالب"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM students
                WHERE full_name LIKE ? OR national_id LIKE ? OR seat_number LIKE ?
                ORDER BY full_name
            ''', (f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'))

            results = cursor.fetchall()
            conn.close()

            return results

        except Exception as e:
            print(f"خطأ في البحث عن الطالب: {e}")
            return []

    def get_all_sections(self):
        """الحصول على جميع الشعب"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT section_name FROM sections
                WHERE status = 'نشط'
                ORDER BY section_name
            ''')

            results = [row[0] for row in cursor.fetchall()]
            conn.close()

            return results

        except Exception as e:
            print(f"خطأ في الحصول على الشعب: {e}")
            return []

    def get_all_subjects(self):
        """الحصول على جميع المواد"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT subject_name FROM subjects
                WHERE status = 'نشط'
                ORDER BY subject_name
            ''')

            results = [row[0] for row in cursor.fetchall()]
            conn.close()

            return results

        except Exception as e:
            print(f"خطأ في الحصول على المواد: {e}")
            return []

    def add_section(self, section_name, section_code=None, capacity=30):
        """إضافة شعبة جديدة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if section_code is None:
                # توليد كود الشعبة تلقائياً
                section_code = f"SEC{len(self.get_all_sections()) + 1:03d}"

            cursor.execute('''
                INSERT INTO sections (section_name, section_code, capacity)
                VALUES (?, ?, ?)
            ''', (section_name, section_code, capacity))

            section_id = cursor.lastrowid
            conn.commit()
            conn.close()

            # تسجيل العملية
            self.log_activity(
                "إضافة شعبة",
                "sections",
                section_id,
                None,
                {"section_name": section_name, "section_code": section_code}
            )

            return section_id

        except Exception as e:
            raise Exception(f"خطأ في إضافة الشعبة: {str(e)}")

    def add_subject(self, subject_name, subject_code=None, credit_hours=3,
                   max_grade=100, passing_grade=50, subject_type="نظري"):
        """إضافة مادة جديدة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if subject_code is None:
                # توليد كود المادة تلقائياً
                subject_code = f"SUB{len(self.get_all_subjects()) + 1:03d}"

            cursor.execute('''
                INSERT INTO subjects
                (subject_name, subject_code, credit_hours, max_grade,
                 passing_grade, subject_type)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (subject_name, subject_code, credit_hours, max_grade,
                  passing_grade, subject_type))

            subject_id = cursor.lastrowid
            conn.commit()
            conn.close()

            # تسجيل العملية
            self.log_activity(
                "إضافة مادة",
                "subjects",
                subject_id,
                None,
                {
                    "subject_name": subject_name,
                    "subject_code": subject_code,
                    "credit_hours": credit_hours
                }
            )

            return subject_id

        except Exception as e:
            raise Exception(f"خطأ في إضافة المادة: {str(e)}")

    def save_grade(self, student_id, subject_id, grade_data, academic_year):
        """حفظ درجات طالب"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود الدرجة مسبقاً
            cursor.execute('''
                SELECT id FROM grades
                WHERE student_id = ? AND subject_id = ? AND academic_year = ?
            ''', (student_id, subject_id, academic_year))

            existing_grade = cursor.fetchone()

            # حساب الدرجة النهائية
            final_grade = (
                grade_data.get('semester1_work', 0) +
                grade_data.get('semester1_exam', 0) +
                grade_data.get('semester2_work', 0) +
                grade_data.get('semester2_exam', 0)
            )

            # تحديد حالة النجاح
            is_passed = final_grade >= 50  # يمكن تخصيصها حسب المادة

            if existing_grade:
                # تحديث الدرجة الموجودة
                cursor.execute('''
                    UPDATE grades SET
                        semester1_work = ?, semester1_exam = ?,
                        semester2_work = ?, semester2_exam = ?,
                        second_round_exam = ?, is_passed = ?,
                        passed_in_second_round = ?, last_updated = CURRENT_TIMESTAMP
                    WHERE student_id = ? AND subject_id = ? AND academic_year = ?
                ''', (
                    grade_data.get('semester1_work', 0),
                    grade_data.get('semester1_exam', 0),
                    grade_data.get('semester2_work', 0),
                    grade_data.get('semester2_exam', 0),
                    grade_data.get('second_round_exam', 0),
                    is_passed,
                    grade_data.get('passed_in_second_round', False),
                    student_id, subject_id, academic_year
                ))
            else:
                # إضافة درجة جديدة
                cursor.execute('''
                    INSERT INTO grades
                    (student_id, subject_id, academic_year, semester1_work,
                     semester1_exam, semester2_work, semester2_exam,
                     second_round_exam, is_passed, passed_in_second_round)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    student_id, subject_id, academic_year,
                    grade_data.get('semester1_work', 0),
                    grade_data.get('semester1_exam', 0),
                    grade_data.get('semester2_work', 0),
                    grade_data.get('semester2_exam', 0),
                    grade_data.get('second_round_exam', 0),
                    is_passed,
                    grade_data.get('passed_in_second_round', False)
                ))

            conn.commit()
            conn.close()

            # تسجيل العملية
            self.log_activity(
                "حفظ درجات",
                "grades",
                f"{student_id}-{subject_id}",
                None,
                grade_data
            )

            return True

        except Exception as e:
            raise Exception(f"خطأ في حفظ الدرجات: {str(e)}")

    def get_student_grades(self, student_id, academic_year=None):
        """الحصول على درجات طالب"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            query = '''
                SELECT s.subject_name, g.semester1_work, g.semester1_exam,
                       g.semester2_work, g.semester2_exam, g.final_grade,
                       g.second_round_exam, g.is_passed, g.passed_in_second_round,
                       g.academic_year
                FROM grades g
                JOIN subjects s ON g.subject_id = s.id
                WHERE g.student_id = ?
            '''

            params = [student_id]

            if academic_year:
                query += " AND g.academic_year = ?"
                params.append(academic_year)

            query += " ORDER BY s.subject_name"

            cursor.execute(query, params)
            results = cursor.fetchall()
            conn.close()

            return results

        except Exception as e:
            print(f"خطأ في الحصول على درجات الطالب: {e}")
            return []
