#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد نظام إدارة الطلاب
Student Management System Setup

معهد المتوسط للمهن الشاملة - اجخرة
"""

from setuptools import setup, find_packages
import os

# قراءة ملف README
def read_readme():
    try:
        with open('README.md', 'r', encoding='utf-8') as f:
            return f.read()
    except:
        return "نظام إدارة الطلاب - معهد المتوسط للمهن الشاملة"

# قراءة متطلبات التطبيق
def read_requirements():
    try:
        with open('requirements.txt', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # تصفية التعليقات والأسطر الفارغة
        requirements = []
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#') and not line.startswith('python_requires'):
                requirements.append(line)
        
        return requirements
    except:
        return []

setup(
    name="student-management-system",
    version="2.0.0",
    description="نظام إدارة الطلاب - معهد المتوسط للمهن الشاملة",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    
    # معلومات المطور
    author="معهد المتوسط للمهن الشاملة",
    author_email="<EMAIL>",
    
    # معلومات المشروع
    url="https://github.com/ajkhara-institute/student-management-system",
    project_urls={
        "Bug Reports": "https://github.com/ajkhara-institute/student-management-system/issues",
        "Source": "https://github.com/ajkhara-institute/student-management-system",
    },
    
    # تصنيف المشروع
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Education",
        "Topic :: Education :: Computer Aided Instruction (CAI)",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.6",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: OS Independent",
        "Natural Language :: Arabic",
    ],
    
    # الكلمات المفتاحية
    keywords="student management system education arabic institute",
    
    # الحزم والملفات
    packages=find_packages(),
    include_package_data=True,
    package_data={
        '': ['*.txt', '*.md', '*.json', '*.ico', '*.png', '*.jpg'],
        'resources': ['*'],
        'resources/icons': ['*'],
        'resources/templates': ['*'],
        'resources/fonts': ['*'],
    },
    
    # المتطلبات
    python_requires='>=3.6',
    install_requires=read_requirements(),
    
    # نقطة الدخول
    entry_points={
        'console_scripts': [
            'student-management=main:main',
        ],
        'gui_scripts': [
            'student-management-gui=main:main',
        ],
    },
    
    # ملفات إضافية
    data_files=[
        ('', ['README.md', 'requirements.txt']),
        ('docs', ['docs/user_guide.md'] if os.path.exists('docs/user_guide.md') else []),
    ],
    
    # خيارات إضافية
    zip_safe=False,
    
    # معلومات الترخيص
    license="MIT",
    
    # منصات مدعومة
    platforms=["Windows", "macOS", "Linux"],
    
    # معلومات إضافية
    extras_require={
        'dev': [
            'pytest>=6.0',
            'pytest-cov>=2.0',
            'black>=21.0',
            'flake8>=3.8',
        ],
        'build': [
            'pyinstaller>=4.0',
            'cx_Freeze>=6.0',
        ],
    },
)

# معلومات ما بعد التثبيت
print("""
================================================
نظام إدارة الطلاب
معهد المتوسط للمهن الشاملة - اجخرة
الإصدار 2.0
================================================

تم تثبيت النظام بنجاح!

للتشغيل:
1. انقر نقراً مزدوجاً على run_app.bat (Windows)
2. أو استخدم الأمر: python main.py

للحصول على المساعدة:
- اقرأ ملف README.md
- راجع دليل المستخدم في مجلد docs
- تواصل مع إدارة النظام

================================================
""")
