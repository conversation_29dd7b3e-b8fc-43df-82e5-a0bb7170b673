STUDENT MANAGEMENT SYSTEM - QUICK START GUIDE
============================================

🚀 HOW TO RUN THE PROGRAM:
=========================

Method 1 (Easiest):
Double-click: run.bat

Method 2:
Double-click: start_student_system.bat

Method 3 (PowerShell):
Right-click start_system.ps1 → "Run with PowerShell"

Method 4 (Command Line):
python run_student_system.py

🔧 IF THE PROGRAM DOESN'T WORK:
==============================

Step 1: Run Diagnostic Tool
python تشخيص_المشاكل.py

Step 2: Run Quick Fix
python إصلاح_سريع.py

Step 3: Try Again
python run_student_system.py

🚫 COMMON PROBLEMS & SOLUTIONS:
==============================

Problem: "python is not recognized"
Solution:
1. Install Python from https://python.org
2. During installation, check "Add Python to PATH"
3. Restart computer
4. Try again

Problem: Batch file shows strange characters
Solution:
1. Use run.bat instead
2. Or run directly: python run_student_system.py

Problem: "No module named 'tkinter'"
Solution:
1. Reinstall Python with tkinter
2. On Linux: sudo apt-get install python3-tk

Problem: Window doesn't appear
Solution:
1. Check error messages in command prompt
2. Run as administrator
3. Check antivirus isn't blocking

Problem: Database error
Solution:
1. Delete 'data' folder completely
2. Restart program
3. New database will be created

🎯 FIRST TIME SETUP:
===================

1. Run the program
2. Go to "Settings" (إعدادات النظام)
3. Enter institute information
4. Add sections/classes
5. Start adding student data

📞 FOR HELP:
============

1. Check تعليمات_التشغيل.txt (detailed Arabic guide)
2. Run diagnostic tool for detailed report
3. Make sure Python is updated to latest version

FILES TO RUN:
- run.bat (simplest)
- start_student_system.bat (with error checking)
- start_system.ps1 (PowerShell version)
- python run_student_system.py (direct command)

DIAGNOSTIC FILES:
- تشخيص_المشاكل.py (diagnostic tool)
- إصلاح_سريع.py (quick fix tool)
