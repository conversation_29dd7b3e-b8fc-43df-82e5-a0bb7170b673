#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام اختيار الطلاب المركزي
Centralized Student Selection System
"""

class StudentSelector:
    """نظام اختيار الطلاب المركزي لتمكين العمليات عبر النوافذ المختلفة"""
    
    _instance = None
    _selected_student = None
    _observers = []
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(StudentSelector, cls).__new__(cls)
        return cls._instance
    
    def select_student(self, student_data):
        """
        اختيار طالب
        student_data: tuple (id, full_name, national_id, seat_number, section, birth_date, gender, phone)
        """
        self._selected_student = {
            'id': student_data[0],
            'full_name': student_data[1],
            'national_id': student_data[2],
            'seat_number': student_data[3],
            'section': student_data[4],
            'birth_date': student_data[5] if len(student_data) > 5 else '',
            'gender': student_data[6] if len(student_data) > 6 else '',
            'phone': student_data[7] if len(student_data) > 7 else ''
        }
        self._notify_observers()
    
    def get_selected_student(self):
        """الحصول على الطالب المحدد"""
        return self._selected_student
    
    def clear_selection(self):
        """مسح الاختيار"""
        self._selected_student = None
        self._notify_observers()
    
    def is_student_selected(self):
        """التحقق من وجود طالب محدد"""
        return self._selected_student is not None
    
    def add_observer(self, callback):
        """إضافة مراقب للتغييرات"""
        if callback not in self._observers:
            self._observers.append(callback)
    
    def remove_observer(self, callback):
        """إزالة مراقب"""
        if callback in self._observers:
            self._observers.remove(callback)
    
    def _notify_observers(self):
        """إشعار جميع المراقبين بالتغيير"""
        for callback in self._observers:
            try:
                callback(self._selected_student)
            except Exception as e:
                print(f"خطأ في إشعار المراقب: {e}")

# إنشاء مثيل مفرد
student_selector = StudentSelector()
