# متطلبات نظام إدارة الطلاب
# Student Management System Requirements

# المكتبات الأساسية (مثبتة افتراضياً مع Python)
# tkinter - واجهة المستخدم الرسومية
# sqlite3 - قاعدة البيانات
# datetime - التعامل مع التواريخ
# hashlib - التشفير
# json - التعامل مع ملفات JSON
# os - عمليات نظام التشغيل
# sys - معلومات النظام
# platform - معلومات المنصة

# ملاحظة: جميع المكتبات المطلوبة مثبتة افتراضياً مع Python 3.6+
# لا حاجة لتثبيت مكتبات إضافية

# للتشغيل:
# python run_student_system.py

# أو استخدم ملف الـ batch على Windows:
# تشغيل_نظام_الطلاب.bat
