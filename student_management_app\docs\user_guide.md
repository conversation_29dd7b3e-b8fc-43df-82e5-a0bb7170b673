# دليل المستخدم - نظام إدارة الطلاب

## معهد المتوسط للمهن الشاملة - اجخرة

### الإصدار 2.0

---

## المحتويات

1. [مقدمة](#مقدمة)
2. [متطلبات النظام](#متطلبات-النظام)
3. [التثبيت والتشغيل](#التثبيت-والتشغيل)
4. [الواجهة الرئيسية](#الواجهة-الرئيسية)
5. [إدارة الطلاب](#إدارة-الطلاب)
6. [إدارة الدرجات](#إدارة-الدرجات)
7. [البحث والاستعلام](#البحث-والاستعلام)
8. [الشهادات والتقارير](#الشهادات-والتقارير)
9. [الإعدادات](#الإعدادات)
10. [النسخ الاحتياطي](#النسخ-الاحتياطي)
11. [حل المشاكل](#حل-المشاكل)

---

## مقدمة

نظام إدارة الطلاب هو تطبيق شامل مصمم خصيصاً لمعهد المتوسط للمهن الشاملة في اجخرة. يوفر النظام جميع الأدوات اللازمة لإدارة بيانات الطلاب، الدرجات، والتقارير بطريقة فعالة ومنظمة.

### الميزات الرئيسية:
- إدارة شاملة لبيانات الطلاب
- نظام درجات متقدم مع حساب تلقائي للنتائج
- بحث سريع ومتقدم
- إنتاج الشهادات والتقارير
- نسخ احتياطي تلقائي
- واجهة عربية سهلة الاستخدام

---

## متطلبات النظام

### الحد الأدنى:
- **نظام التشغيل**: Windows 7/8/10/11، macOS 10.12+، أو Linux
- **Python**: الإصدار 3.6 أو أحدث
- **الذاكرة**: 512 MB RAM
- **مساحة القرص**: 100 MB مساحة فارغة
- **الشاشة**: دقة 1024x768 أو أعلى

### المستحسن:
- **نظام التشغيل**: Windows 10/11
- **Python**: الإصدار 3.9 أو أحدث
- **الذاكرة**: 2 GB RAM أو أكثر
- **مساحة القرص**: 500 MB مساحة فارغة
- **الشاشة**: دقة 1366x768 أو أعلى

---

## التثبيت والتشغيل

### الطريقة الأولى (الأسهل):
1. انقر نقراً مزدوجاً على ملف `run_app.bat`
2. انتظر حتى يتم تحميل النظام

### الطريقة الثانية:
1. افتح موجه الأوامر (Command Prompt)
2. انتقل إلى مجلد التطبيق
3. اكتب الأمر: `python main.py`
4. اضغط Enter

### التشغيل لأول مرة:
- سيقوم النظام بإنشاء قاعدة البيانات تلقائياً
- سيتم إضافة بيانات افتراضية (شعب ومواد)
- ستظهر الواجهة الرئيسية للنظام

---

## الواجهة الرئيسية

### العناصر الرئيسية:

#### 1. رأس النافذة:
- عنوان النظام
- اسم المعهد والموقع
- السنة الدراسية الحالية

#### 2. لوحة الوصول السريع:
- **طالب جديد**: إضافة طالب جديد بسرعة
- **بحث سريع**: البحث عن طالب
- **إحصائيات**: عرض الإحصائيات
- **نسخة احتياطية**: إنشاء نسخة احتياطية سريعة

#### 3. الوظائف الرئيسية:
- **إدارة الطلاب**: إضافة وتعديل وحذف بيانات الطلاب
- **إدارة الدرجات**: إدخال وتعديل درجات الطلاب
- **البحث والاستعلام**: البحث المتقدم عن الطلاب
- **الشهادات والتقارير**: إنتاج الشهادات والتقارير
- **بطاقات الجلوس**: إنتاج بطاقات أرقام الجلوس
- **إعدادات النظام**: تخصيص إعدادات التطبيق

#### 4. لوحة الإحصائيات:
- إجمالي عدد الطلاب
- عدد الشعب
- توزيع الطلاب على الشعب

#### 5. شريط الحالة:
- حالة النظام الحالية
- التاريخ والوقت

---

## إدارة الطلاب

### إضافة طالب جديد:

1. **اضغط على "إدارة الطلاب"** من الواجهة الرئيسية
2. **اضغط على "طالب جديد"**
3. **املأ البيانات المطلوبة**:
   - الاسم الكامل (مطلوب)
   - الرقم الوطني (مطلوب وفريد)
   - الشعبة (مطلوب)
   - الجنس (مطلوب)
   - تاريخ الميلاد (اختياري)
   - رقم الهاتف (اختياري)
   - العنوان (اختياري)
   - اسم ولي الأمر (اختياري)
   - رقم هاتف ولي الأمر (اختياري)
   - ملاحظات (اختياري)

4. **اضغط على "حفظ"**
5. سيتم توليد رقم الجلوس تلقائياً

### تعديل بيانات طالب:

1. **ابحث عن الطالب** باستخدام الاسم أو الرقم الوطني
2. **اضغط على "تعديل"**
3. **عدّل البيانات المطلوبة**
4. **اضغط على "حفظ التعديلات"**

### حذف طالب:

1. **ابحث عن الطالب**
2. **اضغط على "حذف"**
3. **أكد عملية الحذف**

⚠️ **تحذير**: حذف الطالب سيؤدي إلى حذف جميع درجاته أيضاً

---

## إدارة الدرجات

### إدخال الدرجات:

1. **اضغط على "إدارة الدرجات"**
2. **اختر الشعبة** من القائمة المنسدلة
3. **اختر المادة** من القائمة المنسدلة
4. **اختر السنة الدراسية**
5. **اضغط على "تحميل الطلاب"**
6. **أدخل الدرجات** في الحقول المناسبة:
   - أعمال الفصل الأول
   - امتحان الفصل الأول
   - أعمال الفصل الثاني
   - امتحان الفصل الثاني
   - امتحان الدور الثاني (إذا لزم الأمر)

7. **اضغط على "حفظ الدرجات"**

### نظام الدرجات:

- **الدرجة الكاملة**: 100 درجة (افتراضياً)
- **درجة النجاح**: 50 درجة (افتراضياً)
- **الحساب التلقائي**: يتم حساب الدرجة النهائية تلقائياً
- **حالة النجاح**: يتم تحديدها تلقائياً بناءً على الدرجة النهائية

---

## البحث والاستعلام

### البحث السريع:

1. **اضغط على "بحث سريع"** من لوحة الوصول السريع
2. **أدخل كلمة البحث**:
   - الاسم (كامل أو جزئي)
   - الرقم الوطني
   - رقم الجلوس
3. **اضغط على "بحث"**
4. **انقر نقراً مزدوجاً** على النتيجة لعرض التفاصيل

### البحث المتقدم:

1. **اضغط على "البحث والاستعلام"**
2. **استخدم خيارات البحث المتقدمة**:
   - البحث بالشعبة
   - البحث بالجنس
   - البحث بحالة الطالب
   - البحث بالسنة الدراسية

### عرض درجات الطالب:

1. **ابحث عن الطالب**
2. **انقر نقراً مزدوجاً** على اسم الطالب
3. ستظهر نافذة تحتوي على:
   - بيانات الطالب الأساسية
   - جميع درجاته في المواد المختلفة
   - حالة النجاح/الرسوب
   - المعدل العام

---

## الشهادات والتقارير

### إنتاج الشهادات:

1. **اضغط على "الشهادات والتقارير"**
2. **اختر نوع الشهادة**:
   - شهادة درجات
   - شهادة نجاح
   - شهادة مشاركة

3. **اختر الطلاب**:
   - طالب واحد
   - شعبة كاملة
   - مجموعة مختارة

4. **اختر السنة الدراسية**
5. **اضغط على "إنتاج الشهادات"**
6. **اختر مكان الحفظ**

### أنواع التقارير:

- **تقرير الدرجات**: قائمة بدرجات الطلاب
- **تقرير الإحصائيات**: إحصائيات شاملة
- **تقرير الحضور**: سجل حضور الطلاب
- **تقرير النجاح/الرسوب**: قوائم النجاح والرسوب

---

## الإعدادات

### إعدادات المعهد:

- **اسم المعهد**: يمكن تعديله
- **الموقع**: مكان المعهد
- **رقم الهاتف**: للتواصل
- **البريد الإلكتروني**: للمراسلات

### إعدادات النظام:

- **السنة الدراسية**: تحديد السنة الحالية
- **الفصل الدراسي**: الأول أو الثاني
- **درجة النجاح**: الحد الأدنى للنجاح
- **الدرجة الكاملة**: أقصى درجة ممكنة

### إعدادات الواجهة:

- **حجم الخط**: تكبير أو تصغير النص
- **نمط الألوان**: اختيار نمط الألوان
- **اللغة**: العربية (افتراضي)

---

## النسخ الاحتياطي

### إنشاء نسخة احتياطية:

#### الطريقة السريعة:
1. **اضغط على "نسخة احتياطية"** من لوحة الوصول السريع
2. سيتم إنشاء النسخة تلقائياً في مجلد `backups`

#### الطريقة المتقدمة:
1. **اضغط على "إعدادات النظام"**
2. **اختر "النسخ الاحتياطي"**
3. **اختر مكان الحفظ**
4. **أدخل وصف للنسخة**
5. **اضغط على "إنشاء نسخة احتياطية"**

### استعادة نسخة احتياطية:

1. **اضغط على "إعدادات النظام"**
2. **اختر "استعادة نسخة احتياطية"**
3. **اختر ملف النسخة الاحتياطية**
4. **أكد عملية الاستعادة**

⚠️ **تحذير**: استعادة النسخة الاحتياطية ستحل محل البيانات الحالية

### النسخ الاحتياطي التلقائي:

- يتم إنشاء نسخة احتياطية تلقائياً كل أسبوع
- يتم حفظ النسخ في مجلد `data/backups`
- يمكن تعديل فترة النسخ الاحتياطي من الإعدادات

---

## حل المشاكل

### مشاكل شائعة وحلولها:

#### 1. النظام لا يبدأ:
- **تأكد من تثبيت Python 3.6+**
- **تحقق من وجود جميع الملفات**
- **أعد تشغيل الكمبيوتر**

#### 2. خطأ في قاعدة البيانات:
- **أغلق النظام تماماً**
- **احذف ملف `data/student_data.db`**
- **أعد تشغيل النظام**
- **استعد نسخة احتياطية إذا كانت متوفرة**

#### 3. مشاكل في الخطوط العربية:
- **تأكد من تثبيت خط Tahoma**
- **أعد تشغيل النظام**
- **تحقق من إعدادات اللغة في Windows**

#### 4. بطء في الأداء:
- **أغلق البرامج الأخرى**
- **أنشئ نسخة احتياطية وأعد تثبيت النظام**
- **تحقق من مساحة القرص الصلب**

#### 5. مشاكل في الطباعة:
- **تأكد من تثبيت الطابعة**
- **تحقق من إعدادات الطابعة**
- **جرب طباعة من برنامج آخر**

### الحصول على المساعدة:

- **راجع هذا الدليل أولاً**
- **تواصل مع إدارة النظام في المعهد**
- **احتفظ بنسخة احتياطية دائماً**

---

## معلومات إضافية

### اختصارات لوحة المفاتيح:

- **Ctrl+N**: طالب جديد
- **Ctrl+F**: بحث سريع
- **Ctrl+S**: حفظ
- **Ctrl+B**: نسخة احتياطية
- **F1**: المساعدة
- **Alt+F4**: إغلاق النافذة

### نصائح للاستخدام الأمثل:

1. **أنشئ نسخة احتياطية بانتظام**
2. **تأكد من صحة البيانات قبل الحفظ**
3. **استخدم البحث السريع للوصول للطلاب**
4. **راجع الإحصائيات دورياً**
5. **حدّث إعدادات النظام حسب الحاجة**

---

**© 2024 معهد المتوسط للمهن الشاملة - اجخرة**  
**جميع الحقوق محفوظة**
