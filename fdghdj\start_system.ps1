# Student Management System Launcher
# PowerShell Script

Write-Host "================================================" -ForegroundColor Cyan
Write-Host "Student Management System" -ForegroundColor Green
Write-Host "Institute of Comprehensive Vocational Skills" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Cyan
Write-Host ""

# Check if Python is installed
try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Python found: $pythonVersion" -ForegroundColor Green
    } else {
        throw "Python not found"
    }
} catch {
    Write-Host "Error: Python is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Python from https://python.org" -ForegroundColor Yellow
    Write-Host "Make sure to check 'Add Python to PATH' during installation" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if main file exists
if (-not (Test-Path "run_student_system.py")) {
    Write-Host "Error: run_student_system.py not found" -ForegroundColor Red
    Write-Host "Make sure all program files are in the current directory" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Starting Student Management System..." -ForegroundColor Yellow
Write-Host ""

# Run the program
try {
    python run_student_system.py
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "Program ended successfully" -ForegroundColor Green
    } else {
        throw "Program exited with error code $LASTEXITCODE"
    }
} catch {
    Write-Host ""
    Write-Host "================================================" -ForegroundColor Red
    Write-Host "ERROR: System failed to start" -ForegroundColor Red
    Write-Host "================================================" -ForegroundColor Red
    Write-Host ""
    Write-Host "Suggested solutions:" -ForegroundColor Yellow
    Write-Host "1. Run diagnostic: python تشخيص_المشاكل.py" -ForegroundColor White
    Write-Host "2. Run quick fix: python إصلاح_سريع.py" -ForegroundColor White
    Write-Host "3. Run as administrator" -ForegroundColor White
    Write-Host "4. Check تعليمات_التشغيل.txt for detailed help" -ForegroundColor White
    Write-Host ""
}

Read-Host "Press Enter to exit"
