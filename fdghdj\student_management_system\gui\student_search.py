#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة البحث عن الطلاب
Student Search Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مسار المكتبات
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.arabic_support import *
from utils.student_selector import student_selector

class StudentSearchWindow:
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.setup_ui()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("البحث عن الطلاب")
        self.window.geometry("900x600")
        self.window.configure(bg='#ecf0f1')
        self.window.resizable(True, True)
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # توسيط النافذة
        self.center_window()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg='#2c3e50', height=60)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame,
            text="البحث عن الطلاب",
            font=get_arabic_font(size=18, weight='bold'),
            fg='white',
            bg='#2c3e50'
        )
        title_label.pack(pady=15)
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.window, bg='#ecf0f1')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # إطار البحث
        search_frame = tk.LabelFrame(
            main_frame,
            text="البحث",
            font=get_arabic_font(size=14, weight='bold'),
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        search_frame.pack(fill='x', pady=(0, 20))
        
        # حقل البحث
        search_input_frame = tk.Frame(search_frame, bg='#ecf0f1')
        search_input_frame.pack(fill='x', padx=20, pady=15)
        
        tk.Label(
            search_input_frame,
            text="البحث بالاسم أو الرقم الوطني أو رقم الجلوس:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1',
            fg='#2c3e50'
        ).pack(anchor='e', pady=(0, 5))
        
        search_entry_frame = tk.Frame(search_input_frame, bg='#ecf0f1')
        search_entry_frame.pack(fill='x')
        
        self.search_var = tk.StringVar()
        self.search_entry = RTLEntry(
            search_entry_frame,
            textvariable=self.search_var,
            font=get_arabic_font(size=12),
            width=40
        )
        self.search_entry.pack(side='right', padx=(0, 10))
        self.search_entry.bind('<Return>', lambda e: self.search_students())
        
        search_btn = create_arabic_button(
            search_entry_frame,
            "بحث",
            command=self.search_students,
            bg='#3498db',
            fg='white',
            width=10
        )
        search_btn.pack(side='right', padx=(0, 10))
        
        clear_btn = create_arabic_button(
            search_entry_frame,
            "مسح",
            command=self.clear_search,
            bg='#95a5a6',
            fg='white',
            width=10
        )
        clear_btn.pack(side='right')
        
        # إطار النتائج
        results_frame = tk.LabelFrame(
            main_frame,
            text="نتائج البحث",
            font=get_arabic_font(size=14, weight='bold'),
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        results_frame.pack(fill='both', expand=True)
        
        # جدول النتائج
        self.create_results_table(results_frame)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.window, bg='#ecf0f1')
        buttons_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        select_student_btn = create_arabic_button(
            buttons_frame,
            "اختيار الطالب",
            command=self.select_student_for_operations,
            bg='#3498db',
            fg='white',
            width=15
        )
        select_student_btn.pack(side='right', padx=(0, 10))

        view_grades_btn = create_arabic_button(
            buttons_frame,
            "عرض الدرجات",
            command=self.view_student_grades,
            bg='#27ae60',
            fg='white',
            width=15
        )
        view_grades_btn.pack(side='right', padx=(0, 10))
        
        close_btn = create_arabic_button(
            buttons_frame,
            "إغلاق",
            command=self.window.destroy,
            bg='#e74c3c',
            fg='white',
            width=10
        )
        close_btn.pack(side='left')
        
        # التركيز على حقل البحث
        self.search_entry.focus()
    
    def create_results_table(self, parent):
        """إنشاء جدول النتائج"""
        table_frame = tk.Frame(parent, bg='#ecf0f1')
        table_frame.pack(fill='both', expand=True, padx=20, pady=15)
        
        # الأعمدة
        columns = ('الاسم الكامل', 'الرقم الوطني', 'رقم الجلوس', 'الشعبة')
        
        self.results_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show='headings',
            height=15
        )
        
        # تكوين الأعمدة
        for col in columns:
            self.results_tree.heading(col, text=col, anchor='center')
            self.results_tree.column(col, width=200, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.results_tree.pack(side='right', fill='both', expand=True)
        scrollbar.pack(side='left', fill='y')
        
        # ربط النقر المزدوج
        self.results_tree.bind('<Double-1>', self.on_student_double_click)
    
    def search_students(self):
        """البحث عن الطلاب"""
        search_term = self.search_var.get().strip()
        
        if not search_term:
            messagebox.showwarning("تحذير", "يرجى إدخال كلمة البحث")
            return
        
        try:
            # مسح النتائج السابقة
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)
            
            # البحث في قاعدة البيانات
            results = self.db_manager.search_student(search_term)
            
            if not results:
                messagebox.showinfo("لا توجد نتائج", "لم يتم العثور على أي طلاب مطابقين لكلمة البحث")
                return
            
            # عرض النتائج
            for student in results:
                # student = (id, full_name, national_id, seat_number, section)
                self.results_tree.insert('', 'end', values=(
                    student[1],  # الاسم الكامل
                    student[2],  # الرقم الوطني
                    student[3],  # رقم الجلوس
                    student[4]   # الشعبة
                ), tags=(student[0],))  # حفظ معرف الطالب في tags
            
            # عرض عدد النتائج
            count = len(results)
            messagebox.showinfo("نتائج البحث", f"تم العثور على {count} طالب/طالبة")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في البحث: {str(e)}")
    
    def clear_search(self):
        """مسح البحث والنتائج"""
        self.search_var.set('')
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        self.search_entry.focus()
    
    def on_student_double_click(self, event):
        """معالجة النقر المزدوج على طالب"""
        self.select_student_for_operations()

    def select_student_for_operations(self):
        """اختيار الطالب للعمليات"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار طالب من القائمة")
            return

        try:
            # الحصول على معرف الطالب
            item = self.results_tree.item(selection[0])
            student_id = item['tags'][0]
            student_name = item['values'][0]

            # الحصول على بيانات الطالب الكاملة من قاعدة البيانات
            # البحث عن الطالب في جميع الشعب
            sections = self.db_manager.get_all_sections()
            student_data = None

            for section in sections:
                students = self.db_manager.get_students_by_section(section)
                for student in students:
                    if student[0] == student_id:
                        student_data = student
                        break
                if student_data:
                    break

            if student_data:
                # تحديد الطالب في النظام المركزي
                student_selector.select_student(student_data)
                messagebox.showinfo("تم الاختيار", f"تم اختيار الطالب: {student_name}\nيمكنك الآن إجراء العمليات عليه من النافذة الرئيسية")
            else:
                messagebox.showerror("خطأ", "لم يتم العثور على بيانات الطالب")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في اختيار الطالب: {str(e)}")
    
    def view_student_grades(self):
        """عرض درجات الطالب المحدد"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار طالب من القائمة")
            return
        
        try:
            # الحصول على معرف الطالب
            item = self.results_tree.item(selection[0])
            student_id = item['tags'][0]
            student_name = item['values'][0]
            
            # الحصول على درجات الطالب
            grades = self.db_manager.get_student_grades(student_id)
            
            if not grades:
                messagebox.showinfo("لا توجد درجات", f"لا توجد درجات مسجلة للطالب: {student_name}")
                return
            
            # إنشاء نافذة عرض الدرجات
            self.show_grades_window(student_name, grades)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في عرض الدرجات: {str(e)}")
    
    def show_grades_window(self, student_name, grades):
        """عرض نافذة الدرجات"""
        grades_window = tk.Toplevel(self.window)
        grades_window.title(f"درجات الطالب: {student_name}")
        grades_window.geometry("800x500")
        grades_window.configure(bg='#ecf0f1')
        grades_window.transient(self.window)
        grades_window.grab_set()
        
        # توسيط النافذة
        grades_window.update_idletasks()
        width = grades_window.winfo_width()
        height = grades_window.winfo_height()
        x = (grades_window.winfo_screenwidth() // 2) - (width // 2)
        y = (grades_window.winfo_screenheight() // 2) - (height // 2)
        grades_window.geometry(f'{width}x{height}+{x}+{y}')
        
        # العنوان
        title_frame = tk.Frame(grades_window, bg='#2c3e50', height=50)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame,
            text=f"درجات الطالب: {student_name}",
            font=get_arabic_font(size=16, weight='bold'),
            fg='white',
            bg='#2c3e50'
        )
        title_label.pack(pady=10)
        
        # جدول الدرجات
        table_frame = tk.Frame(grades_window, bg='#ecf0f1')
        table_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        columns = ('المادة', 'عمل ف1', 'امتحان ف1', 'عمل ف2', 'امتحان ف2', 'المجموع', 'النتيجة')
        
        grades_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show='headings',
            height=12
        )
        
        # تكوين الأعمدة
        for col in columns:
            grades_tree.heading(col, text=col, anchor='center')
            grades_tree.column(col, width=100, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=grades_tree.yview)
        grades_tree.configure(yscrollcommand=scrollbar.set)
        
        grades_tree.pack(side='right', fill='both', expand=True)
        scrollbar.pack(side='left', fill='y')
        
        # إضافة البيانات
        for grade in grades:
            # grade = (subject_name, sem1_work, sem1_exam, sem2_work, sem2_exam, second_round, is_passed, passed_second_round, academic_year)
            total = grade[1] + grade[2] + grade[3] + grade[4]  # مجموع الدرجات
            result = "ناجح" if grade[6] else "راسب"
            
            grades_tree.insert('', 'end', values=(
                grade[0],  # المادة
                grade[1],  # عمل ف1
                grade[2],  # امتحان ف1
                grade[3],  # عمل ف2
                grade[4],  # امتحان ف2
                total,     # المجموع
                result     # النتيجة
            ))
        
        # زر الإغلاق
        close_btn = create_arabic_button(
            grades_window,
            "إغلاق",
            command=grades_window.destroy,
            bg='#e74c3c',
            fg='white',
            width=10
        )
        close_btn.pack(pady=10)

# نقطة تشغيل مستقلة لاختبار النافذة
if __name__ == "__main__":
    class DummyDB:
        def search_student(self, term):
            return [
                (1, "أحمد محمد علي", "1234567890", "A001", "الشعبة الأولى"),
                (2, "سارة أحمد محمد", "0987654321", "A002", "الشعبة الأولى"),
            ]
        
        def get_student_grades(self, student_id):
            return [
                ("الرياضيات", 15, 20, 18, 22, 0, True, False, "2023-2024"),
                ("الفيزياء", 12, 18, 16, 20, 0, True, False, "2023-2024"),
            ]

    root = tk.Tk()
    root.withdraw()
    app = StudentSearchWindow(root, DummyDB())
    root.mainloop()
