#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تشخيص مشاكل نظام إدارة الطلاب
Student Management System Diagnostic Tool
"""

import sys
import os
import platform
import tkinter as tk
from tkinter import messagebox

def check_python_version():
    """فحص إصدار Python"""
    print("=" * 50)
    print("فحص إصدار Python")
    print("=" * 50)
    
    version = sys.version_info
    print(f"إصدار Python: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 6):
        print("❌ خطأ: يتطلب Python 3.6 أو أحدث")
        return False
    else:
        print("✅ إصدار Python مناسب")
        return True

def check_tkinter():
    """فحص توفر tkinter"""
    print("\nفحص مكتبة tkinter")
    print("-" * 30)
    
    try:
        import tkinter
        print("✅ مكتبة tkinter متوفرة")
        
        # اختبار إنشاء نافذة
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        root.destroy()
        print("✅ يمكن إنشاء نوافذ tkinter")
        return True
        
    except ImportError:
        print("❌ خطأ: مكتبة tkinter غير متوفرة")
        print("الحل: أعد تثبيت Python مع tkinter")
        return False
    except Exception as e:
        print(f"❌ خطأ في tkinter: {e}")
        return False

def check_required_modules():
    """فحص المكتبات المطلوبة"""
    print("\nفحص المكتبات المطلوبة")
    print("-" * 30)
    
    modules = ['sqlite3', 'datetime', 'hashlib', 'json', 'os', 'sys', 'platform']
    all_ok = True
    
    for module in modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} غير متوفر")
            all_ok = False
    
    return all_ok

def check_project_structure():
    """فحص بنية المشروع"""
    print("\nفحص بنية المشروع")
    print("-" * 30)
    
    required_files = [
        'run_student_system.py',
        'student_management_system/main.py',
        'student_management_system/database/db_manager.py',
        'student_management_system/utils/arabic_support.py',
        'student_management_system/gui/main_window.py',
        'student_management_system/gui/student_entry.py',
        'student_management_system/gui/grade_entry.py',
        'student_management_system/gui/student_search.py',
        'student_management_system/gui/certificate_generator.py',
        'student_management_system/gui/settings_window.py',
        'student_management_system/gui/seat_cards_generator.py'
    ]
    
    all_ok = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} مفقود")
            all_ok = False
    
    return all_ok

def check_permissions():
    """فحص صلاحيات الكتابة"""
    print("\nفحص صلاحيات الكتابة")
    print("-" * 30)
    
    try:
        # إنشاء مجلد البيانات
        data_dir = "data"
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
            print(f"✅ تم إنشاء مجلد {data_dir}")
        else:
            print(f"✅ مجلد {data_dir} موجود")
        
        # اختبار الكتابة
        test_file = os.path.join(data_dir, "test_write.tmp")
        with open(test_file, 'w') as f:
            f.write("test")
        
        os.remove(test_file)
        print("✅ صلاحيات الكتابة متوفرة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في صلاحيات الكتابة: {e}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("\nاختبار قاعدة البيانات")
    print("-" * 30)
    
    try:
        sys.path.append('student_management_system')
        from database.db_manager import DatabaseManager
        
        # اختبار إنشاء قاعدة البيانات
        db_path = "data/test_db.db"
        db = DatabaseManager(db_path)
        print("✅ تم إنشاء قاعدة البيانات")
        
        # اختبار العمليات الأساسية
        sections = db.get_all_sections()
        print(f"✅ تم تحميل {len(sections)} شعبة")
        
        # حذف قاعدة البيانات التجريبية
        if os.path.exists(db_path):
            os.remove(db_path)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_gui():
    """اختبار الواجهة الرسومية"""
    print("\nاختبار الواجهة الرسومية")
    print("-" * 30)
    
    try:
        sys.path.append('student_management_system')
        from utils.arabic_support import get_arabic_font, setup_arabic_font
        
        # اختبار إنشاء نافذة
        root = tk.Tk()
        root.withdraw()
        
        # اختبار الخط العربي
        font = get_arabic_font()
        print("✅ تم إنشاء الخط العربي")
        
        setup_arabic_font(root)
        print("✅ تم إعداد الخط العربي")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الواجهة الرسومية: {e}")
        return False

def run_full_test():
    """تشغيل اختبار شامل"""
    print("\nتشغيل اختبار شامل للنظام")
    print("-" * 30)
    
    try:
        # تشغيل النظام الفعلي
        sys.path.append('student_management_system')
        from main import StudentManagementApp
        
        print("✅ تم استيراد النظام بنجاح")
        print("⚠️  سيتم إغلاق النافذة تلقائياً بعد 3 ثوان...")
        
        # إنشاء التطبيق
        app = StudentManagementApp()
        
        # إغلاق تلقائي بعد 3 ثوان
        app.root.after(3000, app.root.destroy)
        app.root.mainloop()
        
        print("✅ النظام يعمل بشكل صحيح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("أداة تشخيص نظام إدارة الطلاب")
    print("=" * 50)
    print(f"نظام التشغيل: {platform.system()} {platform.release()}")
    print(f"المعمارية: {platform.machine()}")
    print()
    
    # قائمة الفحوصات
    checks = [
        ("إصدار Python", check_python_version),
        ("مكتبة tkinter", check_tkinter),
        ("المكتبات المطلوبة", check_required_modules),
        ("بنية المشروع", check_project_structure),
        ("صلاحيات الكتابة", check_permissions),
        ("قاعدة البيانات", test_database),
        ("الواجهة الرسومية", test_gui),
    ]
    
    results = []
    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ خطأ في فحص {name}: {e}")
            results.append((name, False))
    
    # ملخص النتائج
    print("\n" + "=" * 50)
    print("ملخص نتائج التشخيص")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{name}: {status}")
        if result:
            passed += 1
    
    print(f"\nالنتيجة: {passed}/{total} فحوصات نجحت")
    
    if passed == total:
        print("\n🎉 جميع الفحوصات نجحت! النظام جاهز للعمل")
        
        # اختبار تشغيل كامل
        print("\nهل تريد تشغيل اختبار شامل للنظام؟ (y/n): ", end="")
        try:
            response = input().lower()
            if response in ['y', 'yes', 'نعم', 'ن']:
                run_full_test()
        except:
            pass
    else:
        print(f"\n⚠️  يوجد {total - passed} مشاكل تحتاج إلى إصلاح")
        print("\nالحلول المقترحة:")
        print("1. أعد تثبيت Python مع tkinter")
        print("2. تأكد من وجود جميع ملفات البرنامج")
        print("3. شغل البرنامج كمدير")
        print("4. تحقق من صلاحيات المجلد")
    
    print("\nاضغط Enter للخروج...")
    try:
        input()
    except:
        pass

if __name__ == "__main__":
    main()
