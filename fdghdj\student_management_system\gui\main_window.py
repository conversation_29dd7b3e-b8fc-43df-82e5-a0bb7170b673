#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النافذة الرئيسية لنظام إدارة الطلاب
Main Window for Student Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import sys
import os

# إضافة مسار المكتبات
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.arabic_support import *
from utils.student_selector import student_selector
from gui.student_selector_widget import StudentSelectorWidget
try:
    from gui.student_entry import StudentEntryWindow
except ImportError:
    StudentEntryWindow = None

try:
    from gui.grade_entry import GradeEntryWindow
except ImportError:
    GradeEntryWindow = None

try:
    from gui.student_search import StudentSearchWindow
except ImportError:
    StudentSearchWindow = None

try:
    from gui.certificate_generator import CertificateGenerator
except ImportError:
    CertificateGenerator = None

try:
    from gui.settings_window import SettingsWindow
except ImportError:
    SettingsWindow = None

class MainWindow:
    def __init__(self, root, db_manager):
        self.root = root
        self.db_manager = db_manager
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم الرئيسية"""
        # إطار العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        title_frame.pack(fill='x', pady=(0, 10))
        title_frame.pack_propagate(False)
        
        # العنوان الرئيسي
        title_label = tk.Label(
            title_frame,
            text="نظام إدارة الطلاب",
            font=get_arabic_font(size=24, weight='bold'),
            fg='white',
            bg='#2c3e50'
        )
        title_label.pack(pady=10)
        
        # العنوان الفرعي
        subtitle_label = tk.Label(
            title_frame,
            text="معهد المتوسط للمهن الشاملة - اجخرة",
            font=get_arabic_font(size=14),
            fg='#ecf0f1',
            bg='#2c3e50'
        )
        subtitle_label.pack()
        
        # الإطار الرئيسي للمحتوى
        main_frame = tk.Frame(self.root, bg='#ecf0f1')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # إضافة واجهة اختيار الطلاب
        self.create_student_selector(main_frame)

        # إنشاء الأزرار الرئيسية
        self.create_main_buttons(main_frame)

        # شريط الحالة
        self.create_status_bar()

    def create_student_selector(self, parent):
        """إنشاء واجهة اختيار الطلاب"""
        self.student_selector_widget = StudentSelectorWidget(
            parent,
            self.db_manager,
            on_selection_change=self.on_student_selection_change
        )
        self.student_selector_widget.pack(fill='x', pady=(0, 10))

    def on_student_selection_change(self, selected_student):
        """معالجة تغيير اختيار الطالب"""
        if selected_student:
            # تحديث شريط الحالة
            self.status_label.configure(
                text=f"الطالب المحدد: {selected_student['full_name']} | جاهز"
            )
        else:
            # إعادة تعيين شريط الحالة
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
            self.status_label.configure(
                text=f"التاريخ والوقت: {current_time} | جاهز"
            )

    def create_main_buttons(self, parent):
        """إنشاء الأزرار الرئيسية"""
        # إطار الأزرار
        buttons_frame = tk.Frame(parent, bg='#ecf0f1')
        buttons_frame.pack(expand=True, fill='both')
        
        # تكوين الشبكة
        for i in range(3):
            buttons_frame.grid_columnconfigure(i, weight=1)
        for i in range(3):
            buttons_frame.grid_rowconfigure(i, weight=1)
        
        # بيانات الأزرار
        buttons_data = [
            {
                'text': 'إدخال بيانات الطلاب',
                'icon': '👥',
                'command': self.open_student_entry,
                'color': '#3498db',
                'row': 0, 'col': 0
            },
            {
                'text': 'إدخال الدرجات',
                'icon': '📝',
                'command': self.open_grade_entry,
                'color': '#e74c3c',
                'row': 0, 'col': 1
            },
            {
                'text': 'البحث عن طالب',
                'icon': '🔍',
                'command': self.open_student_search,
                'color': '#f39c12',
                'row': 0, 'col': 2
            },
            {
                'text': 'إنتاج الشهادات',
                'icon': '📜',
                'command': self.open_certificate_generator,
                'color': '#27ae60',
                'row': 1, 'col': 0
            },
            {
                'text': 'بطاقات أرقام الجلوس',
                'icon': '🎫',
                'command': self.generate_seat_cards,
                'color': '#9b59b6',
                'row': 1, 'col': 1
            },
            {
                'text': 'إعدادات النظام',
                'icon': '⚙️',
                'command': self.open_settings,
                'color': '#34495e',
                'row': 1, 'col': 2
            }
        ]
        
        # إنشاء الأزرار
        for button_info in buttons_data:
            self.create_main_button(
                buttons_frame,
                button_info['text'],
                button_info['icon'],
                button_info['command'],
                button_info['color'],
                button_info['row'],
                button_info['col']
            )
        
        # إحصائيات سريعة
        self.create_quick_stats(buttons_frame)
        
    def create_main_button(self, parent, text, icon, command, color, row, col):
        """إنشاء زر رئيسي"""
        button_frame = tk.Frame(parent, bg=color, relief='raised', bd=2)
        button_frame.grid(row=row, column=col, padx=10, pady=10, sticky='nsew')
        
        # جعل الإطار قابل للنقر
        button_frame.bind('<Button-1>', lambda e: command())
        button_frame.bind('<Enter>', lambda e: button_frame.configure(bg=self.darken_color(color)))
        button_frame.bind('<Leave>', lambda e: button_frame.configure(bg=color))
        
        # الأيقونة
        icon_label = tk.Label(
            button_frame,
            text=icon,
            font=('Arial', 32),
            bg=color,
            fg='white'
        )
        icon_label.pack(pady=(20, 10))
        icon_label.bind('<Button-1>', lambda e: command())
        
        # النص
        text_label = tk.Label(
            button_frame,
            text=text,
            font=get_arabic_font(size=14, weight='bold'),
            bg=color,
            fg='white',
            wraplength=150
        )
        text_label.pack(pady=(0, 20))
        text_label.bind('<Button-1>', lambda e: command())
        
    def create_quick_stats(self, parent):
        """إنشاء إحصائيات سريعة"""
        stats_frame = tk.Frame(parent, bg='#bdc3c7', relief='sunken', bd=2)
        stats_frame.grid(row=2, column=0, columnspan=3, padx=10, pady=10, sticky='ew')
        
        # عنوان الإحصائيات
        stats_title = tk.Label(
            stats_frame,
            text="إحصائيات سريعة",
            font=get_arabic_font(size=16, weight='bold'),
            bg='#bdc3c7',
            fg='#2c3e50'
        )
        stats_title.pack(pady=10)
        
        # إطار الإحصائيات
        stats_content = tk.Frame(stats_frame, bg='#bdc3c7')
        stats_content.pack(fill='x', padx=20, pady=(0, 10))
        
        # الحصول على الإحصائيات
        self.update_stats(stats_content)
        
    def update_stats(self, parent):
        """تحديث الإحصائيات"""
        # مسح المحتوى السابق
        for widget in parent.winfo_children():
            widget.destroy()
        
        try:
            # الحصول على عدد الطلاب
            sections = self.db_manager.get_all_sections()
            total_students = 0
            
            for section in sections:
                students = self.db_manager.get_students_by_section(section)
                total_students += len(students)
            
            # عرض الإحصائيات
            stats_text = f"إجمالي الطلاب: {total_students} | عدد الشعب: {len(sections)}"
            
            stats_label = tk.Label(
                parent,
                text=stats_text,
                font=get_arabic_font(size=12),
                bg='#bdc3c7',
                fg='#2c3e50'
            )
            stats_label.pack()
            
        except Exception as e:
            error_label = tk.Label(
                parent,
                text="خطأ في تحميل الإحصائيات",
                font=get_arabic_font(size=12),
                bg='#bdc3c7',
                fg='#e74c3c'
            )
            error_label.pack()
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = tk.Frame(self.root, bg='#34495e', height=30)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)
        
        # تاريخ ووقت النظام
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
        
        self.status_label = tk.Label(
            status_frame,
            text=f"التاريخ والوقت: {current_time} | جاهز",
            font=get_arabic_font(size=10),
            bg='#34495e',
            fg='white',
            anchor='e'
        )
        self.status_label.pack(fill='x', padx=10, pady=5)
        
        # تحديث الوقت كل دقيقة
        self.update_time()
    
    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
        self.status_label.configure(text=f"التاريخ والوقت: {current_time} | جاهز")
        self.root.after(60000, self.update_time)  # تحديث كل دقيقة
    
    def darken_color(self, color):
        """تغميق اللون للتأثير عند التمرير"""
        color_map = {
            '#3498db': '#2980b9',
            '#e74c3c': '#c0392b',
            '#f39c12': '#e67e22',
            '#27ae60': '#229954',
            '#9b59b6': '#8e44ad',
            '#34495e': '#2c3e50'
        }
        return color_map.get(color, color)
    
    # دوال فتح النوافذ المختلفة
    def open_student_entry(self):
        """فتح نافذة إدخال بيانات الطلاب"""
        try:
            StudentEntryWindow(self.root, self.db_manager)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة إدخال الطلاب: {str(e)}")
    
    def open_grade_entry(self):
        """فتح نافذة إدخال الدرجات"""
        try:
            GradeEntryWindow(self.root, self.db_manager)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة إدخال الدرجات: {str(e)}")
    
    def open_student_search(self):
        """فتح نافذة البحث عن الطلاب"""
        try:
            StudentSearchWindow(self.root, self.db_manager)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة البحث: {str(e)}")
    
    def open_certificate_generator(self):
        """فتح مولد الشهادات"""
        try:
            CertificateGenerator(self.root, self.db_manager)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح مولد الشهادات: {str(e)}")
    
    def generate_seat_cards(self):
        """إنتاج بطاقات أرقام الجلوس"""
        try:
            from gui.seat_cards_generator import SeatCardsGenerator
            SeatCardsGenerator(self.root, self.db_manager)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنتاج بطاقات الجلوس: {str(e)}")
    
    def open_settings(self):
        """فتح نافذة الإعدادات"""
        try:
            SettingsWindow(self.root, self.db_manager)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة الإعدادات: {str(e)}")
