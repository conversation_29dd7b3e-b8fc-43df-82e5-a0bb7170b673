#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة اختيار الطلاب
Student Selection Widget
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مسار المكتبات
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.arabic_support import *
from utils.student_selector import student_selector

class StudentSelectorWidget:
    def __init__(self, parent, db_manager, on_selection_change=None):
        self.parent = parent
        self.db_manager = db_manager
        self.on_selection_change = on_selection_change
        self.selected_student_data = None
        
        # إنشاء الواجهة
        self.create_widget()
        
        # الاشتراك في تغييرات الاختيار
        student_selector.add_observer(self.on_global_selection_change)
        
        # تحديث العرض الحالي
        self.update_selection_display()
    
    def create_widget(self):
        """إنشاء واجهة اختيار الطلاب"""
        # الإطار الرئيسي
        self.main_frame = tk.LabelFrame(
            self.parent,
            text="الطالب المحدد",
            font=get_arabic_font(size=12, weight='bold'),
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        
        # إطار معلومات الطالب المحدد
        self.info_frame = tk.Frame(self.main_frame, bg='#ecf0f1')
        self.info_frame.pack(fill='x', padx=10, pady=5)
        
        # تسمية الطالب المحدد
        self.selected_label = tk.Label(
            self.info_frame,
            text="لم يتم اختيار طالب",
            font=get_arabic_font(size=11),
            bg='#ecf0f1',
            fg='#7f8c8d',
            anchor='e'
        )
        self.selected_label.pack(fill='x', pady=2)
        
        # إطار الأزرار
        self.buttons_frame = tk.Frame(self.main_frame, bg='#ecf0f1')
        self.buttons_frame.pack(fill='x', padx=10, pady=5)
        
        # زر اختيار طالب
        self.select_btn = create_arabic_button(
            self.buttons_frame,
            "اختيار طالب",
            command=self.open_student_selector,
            bg='#3498db',
            fg='white',
            width=12
        )
        self.select_btn.pack(side='right', padx=(0, 5))
        
        # زر تحرير البيانات
        self.edit_btn = create_arabic_button(
            self.buttons_frame,
            "تحرير البيانات",
            command=self.edit_student,
            bg='#f39c12',
            fg='white',
            width=12,
            state='disabled'
        )
        self.edit_btn.pack(side='right', padx=(0, 5))
        
        # زر مسح الاختيار
        self.clear_btn = create_arabic_button(
            self.buttons_frame,
            "مسح الاختيار",
            command=self.clear_selection,
            bg='#e74c3c',
            fg='white',
            width=12,
            state='disabled'
        )
        self.clear_btn.pack(side='left')
    
    def pack(self, **kwargs):
        """تخطيط الواجهة"""
        self.main_frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        """تخطيط الواجهة بالشبكة"""
        self.main_frame.grid(**kwargs)
    
    def open_student_selector(self):
        """فتح نافذة اختيار الطلاب"""
        selector_window = StudentSelectorWindow(self.parent, self.db_manager)
    
    def edit_student(self):
        """تحرير بيانات الطالب المحدد"""
        if not student_selector.is_student_selected():
            messagebox.showwarning("تحذير", "لم يتم اختيار طالب")
            return

        try:
            from gui.student_entry import StudentEntryWindow
            # الحصول على بيانات الطالب المحدد
            selected_student = student_selector.get_selected_student()
            # فتح نافذة تحرير الطالب
            edit_window = StudentEntryWindow(
                self.parent,
                self.db_manager,
                edit_mode=True,
                student_data=selected_student
            )
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة التحرير: {str(e)}")
    
    def clear_selection(self):
        """مسح الاختيار"""
        student_selector.clear_selection()
    
    def on_global_selection_change(self, selected_student):
        """معالجة تغيير الاختيار العام"""
        self.selected_student_data = selected_student
        self.update_selection_display()
        
        # إشعار المستمع الخارجي
        if self.on_selection_change:
            self.on_selection_change(selected_student)
    
    def update_selection_display(self):
        """تحديث عرض الطالب المحدد"""
        if self.selected_student_data:
            # عرض معلومات الطالب
            info_text = f"الطالب: {self.selected_student_data['full_name']}\n"
            info_text += f"رقم الجلوس: {self.selected_student_data['seat_number']}\n"
            info_text += f"الشعبة: {self.selected_student_data['section']}"
            
            self.selected_label.configure(
                text=info_text,
                fg='#2c3e50',
                font=get_arabic_font(size=11, weight='bold')
            )
            
            # تفعيل الأزرار
            self.edit_btn.configure(state='normal')
            self.clear_btn.configure(state='normal')
        else:
            # لا يوجد طالب محدد
            self.selected_label.configure(
                text="لم يتم اختيار طالب",
                fg='#7f8c8d',
                font=get_arabic_font(size=11)
            )
            
            # تعطيل الأزرار
            self.edit_btn.configure(state='disabled')
            self.clear_btn.configure(state='disabled')

class StudentSelectorWindow:
    """نافذة اختيار الطلاب"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.setup_ui()
        self.load_students()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("اختيار طالب")
        self.window.geometry("800x600")
        self.window.configure(bg='#ecf0f1')
        self.window.resizable(True, True)
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # توسيط النافذة
        self.center_window()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg='#2c3e50', height=60)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame,
            text="اختيار طالب",
            font=get_arabic_font(size=18, weight='bold'),
            fg='white',
            bg='#2c3e50'
        )
        title_label.pack(pady=15)
        
        # إطار البحث
        search_frame = tk.Frame(self.window, bg='#ecf0f1')
        search_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Label(
            search_frame,
            text="البحث:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1',
            fg='#2c3e50'
        ).pack(side='right', padx=(0, 10))
        
        self.search_var = tk.StringVar()
        self.search_entry = RTLEntry(
            search_frame,
            textvariable=self.search_var,
            font=get_arabic_font(size=12),
            width=30
        )
        self.search_entry.pack(side='right', padx=(0, 10))
        self.search_entry.bind('<KeyRelease>', self.filter_students)
        
        # إطار الشعبة
        section_frame = tk.Frame(self.window, bg='#ecf0f1')
        section_frame.pack(fill='x', padx=20, pady=(0, 10))
        
        tk.Label(
            section_frame,
            text="الشعبة:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1',
            fg='#2c3e50'
        ).pack(side='right', padx=(0, 10))
        
        self.section_var = tk.StringVar()
        self.section_combo = ttk.Combobox(
            section_frame,
            textvariable=self.section_var,
            font=get_arabic_font(size=12),
            state='readonly',
            width=25
        )
        self.section_combo.pack(side='right')
        self.section_combo.bind('<<ComboboxSelected>>', self.filter_students)
        
        # تحميل الشعب
        self.load_sections()
        
        # جدول الطلاب
        self.create_students_table()
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.window, bg='#ecf0f1')
        buttons_frame.pack(fill='x', padx=20, pady=20)
        
        select_btn = create_arabic_button(
            buttons_frame,
            "اختيار",
            command=self.select_student,
            bg='#27ae60',
            fg='white',
            width=12
        )
        select_btn.pack(side='right', padx=(0, 10))
        
        cancel_btn = create_arabic_button(
            buttons_frame,
            "إلغاء",
            command=self.window.destroy,
            bg='#e74c3c',
            fg='white',
            width=12
        )
        cancel_btn.pack(side='left')
    
    def create_students_table(self):
        """إنشاء جدول الطلاب"""
        table_frame = tk.Frame(self.window, bg='#ecf0f1')
        table_frame.pack(fill='both', expand=True, padx=20, pady=(0, 10))
        
        # الأعمدة
        columns = ('الاسم الكامل', 'رقم الجلوس', 'الرقم الوطني', 'الشعبة')
        
        self.students_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show='headings',
            height=15
        )
        
        # تكوين الأعمدة
        for col in columns:
            self.students_tree.heading(col, text=col, anchor='center')
            self.students_tree.column(col, width=150, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.students_tree.yview)
        self.students_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.students_tree.pack(side='right', fill='both', expand=True)
        scrollbar.pack(side='left', fill='y')
        
        # ربط النقر المزدوج
        self.students_tree.bind('<Double-1>', lambda e: self.select_student())
    
    def load_sections(self):
        """تحميل الشعب"""
        try:
            sections = ['جميع الشعب'] + self.db_manager.get_all_sections()
            self.section_combo['values'] = sections
            self.section_combo.set('جميع الشعب')
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الشعب: {str(e)}")
    
    def load_students(self):
        """تحميل جميع الطلاب"""
        try:
            self.all_students = []
            sections = self.db_manager.get_all_sections()
            
            for section in sections:
                students = self.db_manager.get_students_by_section(section)
                self.all_students.extend(students)
            
            self.display_students(self.all_students)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الطلاب: {str(e)}")
    
    def display_students(self, students):
        """عرض الطلاب في الجدول"""
        # مسح البيانات السابقة
        for item in self.students_tree.get_children():
            self.students_tree.delete(item)
        
        # إضافة الطلاب
        for student in students:
            self.students_tree.insert('', 'end', values=(
                student[1],  # الاسم الكامل
                student[3],  # رقم الجلوس
                student[2],  # الرقم الوطني
                student[4]   # الشعبة
            ), tags=(student,))  # حفظ بيانات الطالب كاملة
    
    def filter_students(self, event=None):
        """تصفية الطلاب حسب البحث والشعبة"""
        search_term = self.search_var.get().lower()
        selected_section = self.section_var.get()
        
        filtered_students = []
        
        for student in self.all_students:
            # تصفية حسب الشعبة
            if selected_section != 'جميع الشعب' and student[4] != selected_section:
                continue
            
            # تصفية حسب البحث
            if search_term:
                if (search_term not in student[1].lower() and  # الاسم
                    search_term not in student[2].lower() and  # الرقم الوطني
                    search_term not in student[3].lower()):   # رقم الجلوس
                    continue
            
            filtered_students.append(student)
        
        self.display_students(filtered_students)
    
    def select_student(self):
        """اختيار الطالب المحدد"""
        selection = self.students_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار طالب من القائمة")
            return
        
        # الحصول على بيانات الطالب
        item = self.students_tree.item(selection[0])
        student_data = item['tags'][0]
        
        # تحديد الطالب في النظام المركزي
        student_selector.select_student(student_data)
        
        # إغلاق النافذة
        self.window.destroy()
        
        messagebox.showinfo("تم الاختيار", f"تم اختيار الطالب: {student_data[1]}")

# نقطة تشغيل مستقلة لاختبار الواجهة
if __name__ == "__main__":
    class DummyDB:
        def get_all_sections(self):
            return ["الشعبة الأولى", "الشعبة الثانية"]
        
        def get_students_by_section(self, section):
            return [
                (1, "أحمد محمد علي", "1234567890", "A001", section, "2000-01-01", "ذكر", "123456789"),
                (2, "سارة أحمد محمد", "0987654321", "A002", section, "2001-02-02", "أنثى", "987654321"),
            ]

    root = tk.Tk()
    root.geometry("400x300")
    
    widget = StudentSelectorWidget(root, DummyDB())
    widget.pack(fill='x', padx=20, pady=20)
    
    root.mainloop()
