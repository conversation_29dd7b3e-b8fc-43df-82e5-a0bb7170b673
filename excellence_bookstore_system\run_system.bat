@echo off
chcp 65001 > nul
title نظام محاسبة مكتبة التميز - المنظومة الإلكترونية

echo ================================================
echo 🏪 نظام محاسبة مكتبة التميز
echo 📊 Excellence Bookstore Accounting System
echo ================================================
echo 🔧 المنظومة الإلكترونية - إعداد محمد مطرود
echo 💻 Electronic System - Prepared by Mohammed Matroud
echo ================================================
echo.

echo 🔍 فحص متطلبات النظام...
echo 🔍 Checking system requirements...

REM فحص وجود Python
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ خطأ: Python غير مثبت على النظام
    echo ❌ Error: Python is not installed
    echo.
    echo 📥 يرجى تحميل وتثبيت Python 3.6 أو أحدث من:
    echo 📥 Please download and install Python 3.6+ from:
    echo 🌐 https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo ✅ Python is available

REM فحص إصدار Python
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo 📋 إصدار Python: %PYTHON_VERSION%
echo 📋 Python version: %PYTHON_VERSION%

echo.
echo 🚀 جاري تشغيل النظام...
echo 🚀 Starting the system...
echo.

REM تشغيل النظام
python main.py

REM معالجة الأخطاء
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ حدث خطأ في تشغيل النظام (رمز الخطأ: %ERRORLEVEL%)
    echo ❌ An error occurred while running the system (Error code: %ERRORLEVEL%)
    echo.
    echo 🔧 الحلول المقترحة:
    echo 🔧 Suggested solutions:
    echo 1. تأكد من وجود جميع ملفات النظام
    echo    Make sure all system files are present
    echo 2. تأكد من صحة إعدادات Python
    echo    Verify Python configuration
    echo 3. أعد تشغيل النظام كمدير
    echo    Run as administrator
    echo.
    pause
    exit /b %ERRORLEVEL%
)

echo.
echo ✅ تم إنهاء النظام بنجاح
echo ✅ System terminated successfully
echo.
echo 📞 للدعم الفني: المنظومة الإلكترونية - محمد مطرود
echo 📞 Technical support: Electronic System - Mohammed Matroud
echo.
pause
