#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
دعم اللغة العربية والخطوط المحسن
Enhanced Arabic Language and Font Support

معهد المتوسط للمهن الشاملة - اجخرة
الإصدار المحسن 2.0
"""

import tkinter as tk
from tkinter import font, ttk
import platform
import os
import re

class ArabicFontManager:
    """مدير الخطوط العربية المحسن"""
    
    def __init__(self):
        self.system = platform.system()
        self.available_fonts = self._get_available_fonts()
        self.default_font_family = self._select_best_font()
        
    def _get_available_fonts(self):
        """الحصول على الخطوط المتاحة حسب نظام التشغيل"""
        if self.system == "Windows":
            return [
                "<PERSON><PERSON><PERSON>", "Arial Unicode MS", "Segoe UI", 
                "Microsoft Sans Serif", "<PERSON><PERSON><PERSON>", "Arial"
            ]
        elif self.system == "Darwin":  # macOS
            return [
                "Al Bayan", "Arial Unicode MS", "Helvetica Neue", 
                "Helvetica", "San Francisco", "Arial"
            ]
        else:  # Linux
            return [
                "DejaVu Sans", "Liberation Sans", "Ubuntu", 
                "Noto Sans Arabic", "Arial", "Helvetica"
            ]
    
    def _select_best_font(self):
        """اختيار أفضل خط متاح"""
        for font_name in self.available_fonts:
            try:
                test_font = font.Font(family=font_name, size=12)
                # اختبار عرض النص العربي
                if self._test_arabic_support(font_name):
                    return font_name
            except:
                continue
        
        return "Arial"  # الخط الافتراضي
    
    def _test_arabic_support(self, font_name):
        """اختبار دعم الخط للعربية"""
        try:
            root = tk.Tk()
            root.withdraw()
            
            test_label = tk.Label(
                root, 
                text="اختبار النص العربي", 
                font=(font_name, 12)
            )
            
            # قياس عرض النص
            test_label.update_idletasks()
            width = test_label.winfo_reqwidth()
            
            root.destroy()
            
            # إذا كان العرض معقولاً، فالخط يدعم العربية
            return width > 50
            
        except:
            return False
    
    def get_font(self, size=11, weight="normal", slant="roman"):
        """الحصول على خط عربي بمواصفات محددة"""
        return font.Font(
            family=self.default_font_family,
            size=size,
            weight=weight,
            slant=slant
        )
    
    def setup_default_fonts(self, root):
        """إعداد الخطوط الافتراضية للتطبيق"""
        try:
            # الخط الافتراضي
            default_font = font.nametofont("TkDefaultFont")
            default_font.configure(
                family=self.default_font_family, 
                size=11
            )
            
            # خط النص
            text_font = font.nametofont("TkTextFont")
            text_font.configure(
                family=self.default_font_family, 
                size=11
            )
            
            # الخط الثابت
            fixed_font = font.nametofont("TkFixedFont")
            fixed_font.configure(
                family=self.default_font_family, 
                size=10
            )
            
            # خط القوائم
            menu_font = font.nametofont("TkMenuFont")
            menu_font.configure(
                family=self.default_font_family, 
                size=10
            )
            
            print(f"تم إعداد الخط العربي: {self.default_font_family}")
            
        except Exception as e:
            print(f"خطأ في إعداد الخطوط: {e}")

# إنشاء مثيل عام من مدير الخطوط
font_manager = ArabicFontManager()

def setup_arabic_font(root):
    """إعداد الخط العربي للتطبيق - الدالة الرئيسية"""
    font_manager.setup_default_fonts(root)
    return font_manager.default_font_family

def get_arabic_font(size=11, weight="normal", slant="roman"):
    """الحصول على خط عربي بمواصفات محددة"""
    return font_manager.get_font(size, weight, slant)

def configure_rtl_text(text_widget):
    """تكوين النص للكتابة من اليمين لليسار"""
    try:
        text_widget.configure(justify='right')
        text_widget.tag_configure("rtl", justify='right')
        text_widget.tag_add("rtl", "1.0", "end")
        
        # إضافة دعم للتمرير الأفقي
        if hasattr(text_widget, 'xview'):
            text_widget.xview_moveto(1.0)
            
    except Exception as e:
        print(f"خطأ في تكوين النص RTL: {e}")

def format_arabic_number(number):
    """تحويل الأرقام الإنجليزية إلى عربية"""
    arabic_digits = "٠١٢٣٤٥٦٧٨٩"
    english_digits = "0123456789"
    
    result = str(number)
    for i, digit in enumerate(english_digits):
        result = result.replace(digit, arabic_digits[i])
    
    return result

def format_english_number(arabic_number):
    """تحويل الأرقام العربية إلى إنجليزية"""
    arabic_digits = "٠١٢٣٤٥٦٧٨٩"
    english_digits = "0123456789"
    
    result = str(arabic_number)
    for i, digit in enumerate(arabic_digits):
        result = result.replace(digit, english_digits[i])
    
    return result

def validate_arabic_input(text):
    """التحقق من صحة النص العربي"""
    if not text:
        return True
    
    # السماح بالأحرف العربية والمسافات والأرقام وعلامات الترقيم
    allowed_pattern = r'^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s0-9٠-٩\.\,\-\(\)\[\]]+$'
    return bool(re.match(allowed_pattern, text))

def validate_national_id(national_id):
    """التحقق من صحة الرقم الوطني"""
    if not national_id:
        return False
    
    # تحويل الأرقام العربية إلى إنجليزية
    national_id = format_english_number(national_id)
    
    # التحقق من أن الرقم يحتوي على أرقام فقط وطوله مناسب
    if not national_id.isdigit():
        return False
    
    # التحقق من طول الرقم الوطني (عادة 10-12 رقم)
    return 10 <= len(national_id) <= 12

def validate_phone_number(phone):
    """التحقق من صحة رقم الهاتف"""
    if not phone:
        return True  # رقم الهاتف اختياري
    
    # تحويل الأرقام العربية إلى إنجليزية
    phone = format_english_number(phone)
    
    # إزالة المسافات والرموز
    phone = re.sub(r'[\s\-\(\)\+]', '', phone)
    
    # التحقق من أن الرقم يحتوي على أرقام فقط وطوله مناسب
    return phone.isdigit() and 7 <= len(phone) <= 15

def format_date_arabic(date_str):
    """تنسيق التاريخ بالعربية"""
    if not date_str:
        return ""
    
    months = {
        '01': 'يناير', '02': 'فبراير', '03': 'مارس', '04': 'أبريل',
        '05': 'مايو', '06': 'يونيو', '07': 'يوليو', '08': 'أغسطس',
        '09': 'سبتمبر', '10': 'أكتوبر', '11': 'نوفمبر', '12': 'ديسمبر'
    }
    
    try:
        # تحليل التاريخ (YYYY-MM-DD)
        parts = date_str.split('-')
        if len(parts) == 3:
            year, month, day = parts
            month_name = months.get(month, month)
            return f"{day} {month_name} {year}"
        else:
            return date_str
    except:
        return date_str

class RTLEntry(tk.Entry):
    """حقل إدخال محسن يدعم الكتابة من اليمين لليسار"""
    
    def __init__(self, parent, **kwargs):
        # إعداد الخصائص الافتراضية للنص العربي
        default_kwargs = {
            'justify': 'right',
            'font': get_arabic_font()
        }
        default_kwargs.update(kwargs)
        
        super().__init__(parent, **default_kwargs)
        
        # ربط الأحداث
        self.bind('<KeyPress>', self._on_key_press)
        self.bind('<FocusIn>', self._on_focus_in)
        self.bind('<FocusOut>', self._on_focus_out)
    
    def _on_key_press(self, event):
        """معالجة ضغط المفاتيح"""
        # يمكن إضافة معالجة خاصة للمفاتيح العربية هنا
        pass
    
    def _on_focus_in(self, event):
        """عند التركيز على الحقل"""
        self.configure(bg='#ffffff')
    
    def _on_focus_out(self, event):
        """عند فقدان التركيز"""
        self.configure(bg='#f8f9fa')

class RTLText(tk.Text):
    """مربع نص محسن يدعم الكتابة من اليمين لليسار"""
    
    def __init__(self, parent, **kwargs):
        # إعداد الخصائص الافتراضية للنص العربي
        default_kwargs = {
            'font': get_arabic_font(),
            'wrap': 'word',
            'undo': True,
            'maxundo': 20
        }
        default_kwargs.update(kwargs)
        
        super().__init__(parent, **default_kwargs)
        
        # تكوين النص للكتابة من اليمين لليسار
        configure_rtl_text(self)
        
        # إضافة قائمة السياق
        self._create_context_menu()
    
    def _create_context_menu(self):
        """إنشاء قائمة السياق"""
        self.context_menu = tk.Menu(self, tearoff=0)
        self.context_menu.add_command(label="نسخ", command=self._copy)
        self.context_menu.add_command(label="لصق", command=self._paste)
        self.context_menu.add_command(label="قص", command=self._cut)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="تحديد الكل", command=self._select_all)
        
        self.bind("<Button-3>", self._show_context_menu)
    
    def _show_context_menu(self, event):
        """عرض قائمة السياق"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()
    
    def _copy(self):
        """نسخ النص المحدد"""
        try:
            self.clipboard_clear()
            self.clipboard_append(self.selection_get())
        except:
            pass
    
    def _paste(self):
        """لصق النص من الحافظة"""
        try:
            text = self.clipboard_get()
            self.insert(tk.INSERT, text)
        except:
            pass
    
    def _cut(self):
        """قص النص المحدد"""
        try:
            self._copy()
            self.delete(tk.SEL_FIRST, tk.SEL_LAST)
        except:
            pass
    
    def _select_all(self):
        """تحديد كامل النص"""
        self.tag_add(tk.SEL, "1.0", tk.END)
        self.mark_set(tk.INSERT, "1.0")
        self.see(tk.INSERT)

def create_arabic_label(parent, text, **kwargs):
    """إنشاء تسمية عربية محسنة"""
    default_kwargs = {
        'font': get_arabic_font(),
        'anchor': 'e',
        'justify': 'right'
    }
    default_kwargs.update(kwargs)
    
    return tk.Label(parent, text=text, **default_kwargs)

def create_arabic_button(parent, text, **kwargs):
    """إنشاء زر بنص عربي محسن"""
    default_kwargs = {
        'font': get_arabic_font(weight='bold'),
        'cursor': 'hand2'
    }
    default_kwargs.update(kwargs)
    
    return tk.Button(parent, text=text, **default_kwargs)

def create_styled_button(parent, text, icon=None, **kwargs):
    """إنشاء زر مُنسق مع أيقونة اختيارية"""
    default_kwargs = {
        'font': get_arabic_font(size=12, weight='bold'),
        'cursor': 'hand2',
        'relief': 'flat',
        'bd': 0,
        'padx': 20,
        'pady': 10
    }
    default_kwargs.update(kwargs)
    
    if icon:
        text = f"{icon} {text}"
    
    return tk.Button(parent, text=text, **default_kwargs)
