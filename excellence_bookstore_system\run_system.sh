#!/bin/bash
# نظام محاسبة مكتبة التميز - ملف تشغيل Linux/macOS
# Excellence Bookstore Accounting System - Linux/macOS Launcher
# المنظومة الإلكترونية - إعداد محمد مطرود

# تعيين الترميز
export LANG=ar_SA.UTF-8
export LC_ALL=ar_SA.UTF-8

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# دالة طباعة ملونة
print_colored() {
    echo -e "${1}${2}${NC}"
}

# رسالة الترحيب
clear
print_colored $CYAN "================================================"
print_colored $CYAN "🏪 نظام محاسبة مكتبة التميز"
print_colored $CYAN "📊 Excellence Bookstore Accounting System"
print_colored $CYAN "================================================"
print_colored $PURPLE "🔧 المنظومة الإلكترونية - إعداد محمد مطرود"
print_colored $PURPLE "💻 Electronic System - Prepared by Mohammed Matroud"
print_colored $CYAN "================================================"
echo

# فحص متطلبات النظام
print_colored $YELLOW "🔍 فحص متطلبات النظام..."
print_colored $YELLOW "🔍 Checking system requirements..."

# فحص وجود Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo
        print_colored $RED "❌ خطأ: Python غير مثبت على النظام"
        print_colored $RED "❌ Error: Python is not installed"
        echo
        print_colored $YELLOW "📥 يرجى تثبيت Python 3.6 أو أحدث:"
        print_colored $YELLOW "📥 Please install Python 3.6+ using:"
        echo
        print_colored $CYAN "Ubuntu/Debian: sudo apt-get install python3"
        print_colored $CYAN "CentOS/RHEL: sudo yum install python3"
        print_colored $CYAN "macOS: brew install python3"
        echo
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

print_colored $GREEN "✅ Python متوفر"
print_colored $GREEN "✅ Python is available"

# فحص إصدار Python
PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
print_colored $BLUE "📋 إصدار Python: $PYTHON_VERSION"
print_colored $BLUE "📋 Python version: $PYTHON_VERSION"

# فحص إصدار Python (يجب أن يكون 3.6+)
PYTHON_MAJOR=$($PYTHON_CMD -c "import sys; print(sys.version_info.major)")
PYTHON_MINOR=$($PYTHON_CMD -c "import sys; print(sys.version_info.minor)")

if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 6 ]); then
    echo
    print_colored $RED "❌ خطأ: يتطلب النظام Python 3.6 أو أحدث"
    print_colored $RED "❌ Error: System requires Python 3.6 or newer"
    print_colored $YELLOW "الإصدار الحالي: $PYTHON_VERSION"
    print_colored $YELLOW "Current version: $PYTHON_VERSION"
    echo
    exit 1
fi

# فحص وجود tkinter
if ! $PYTHON_CMD -c "import tkinter" &> /dev/null; then
    echo
    print_colored $RED "❌ خطأ: مكتبة tkinter غير متوفرة"
    print_colored $RED "❌ Error: tkinter library not available"
    echo
    print_colored $YELLOW "📥 يرجى تثبيت tkinter:"
    print_colored $YELLOW "📥 Please install tkinter:"
    echo
    print_colored $CYAN "Ubuntu/Debian: sudo apt-get install python3-tk"
    print_colored $CYAN "CentOS/RHEL: sudo yum install tkinter"
    print_colored $CYAN "macOS: مثبت افتراضياً / installed by default"
    echo
    exit 1
fi

print_colored $GREEN "✅ جميع المتطلبات متوفرة"
print_colored $GREEN "✅ All requirements satisfied"

echo
print_colored $YELLOW "🚀 جاري تشغيل النظام..."
print_colored $YELLOW "🚀 Starting the system..."
echo

# تشغيل النظام
$PYTHON_CMD main.py

# معالجة الأخطاء
EXIT_CODE=$?
if [ $EXIT_CODE -ne 0 ]; then
    echo
    print_colored $RED "❌ حدث خطأ في تشغيل النظام (رمز الخطأ: $EXIT_CODE)"
    print_colored $RED "❌ An error occurred while running the system (Error code: $EXIT_CODE)"
    echo
    print_colored $YELLOW "🔧 الحلول المقترحة:"
    print_colored $YELLOW "🔧 Suggested solutions:"
    echo "1. تأكد من وجود جميع ملفات النظام"
    echo "   Make sure all system files are present"
    echo "2. تأكد من صحة أذونات الملفات"
    echo "   Verify file permissions"
    echo "3. تأكد من صحة إعدادات Python"
    echo "   Verify Python configuration"
    echo
    read -p "اضغط Enter للخروج / Press Enter to exit..."
    exit $EXIT_CODE
fi

echo
print_colored $GREEN "✅ تم إنهاء النظام بنجاح"
print_colored $GREEN "✅ System terminated successfully"
echo
print_colored $PURPLE "📞 للدعم الفني: المنظومة الإلكترونية - محمد مطرود"
print_colored $PURPLE "📞 Technical support: Electronic System - Mohammed Matroud"
echo

read -p "اضغط Enter للخروج / Press Enter to exit..."
