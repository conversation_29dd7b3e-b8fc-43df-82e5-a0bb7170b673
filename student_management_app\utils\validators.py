#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أدوات التحقق من البيانات
Data Validation Utilities

معهد المتوسط للمهن الشاملة - اجخرة
"""

import re
from datetime import datetime, date
from .arabic_support import format_english_number

class DataValidator:
    """فئة التحقق من البيانات"""
    
    @staticmethod
    def validate_name(name):
        """التحقق من صحة الاسم"""
        if not name or not name.strip():
            return False, "الاسم مطلوب"
        
        name = name.strip()
        
        # التحقق من الطول
        if len(name) < 2:
            return False, "الاسم قصير جداً"
        
        if len(name) > 100:
            return False, "الاسم طويل جداً"
        
        # التحقق من الأحرف المسموحة (عربي وإنجليزي ومسافات)
        allowed_pattern = r'^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\sa-zA-Z\s]+$'
        if not re.match(allowed_pattern, name):
            return False, "الاسم يحتوي على أحرف غير مسموحة"
        
        # التحقق من وجود أحرف عربية على الأقل
        arabic_pattern = r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]'
        if not re.search(arabic_pattern, name):
            return False, "يجب أن يحتوي الاسم على أحرف عربية"
        
        return True, "صحيح"
    
    @staticmethod
    def validate_national_id(national_id):
        """التحقق من صحة الرقم الوطني"""
        if not national_id or not national_id.strip():
            return False, "الرقم الوطني مطلوب"
        
        # تحويل الأرقام العربية إلى إنجليزية
        national_id = format_english_number(national_id.strip())
        
        # التحقق من أن الرقم يحتوي على أرقام فقط
        if not national_id.isdigit():
            return False, "الرقم الوطني يجب أن يحتوي على أرقام فقط"
        
        # التحقق من طول الرقم الوطني
        if len(national_id) < 10:
            return False, "الرقم الوطني قصير جداً"
        
        if len(national_id) > 12:
            return False, "الرقم الوطني طويل جداً"
        
        return True, "صحيح"
    
    @staticmethod
    def validate_phone(phone):
        """التحقق من صحة رقم الهاتف"""
        if not phone or not phone.strip():
            return True, "صحيح"  # رقم الهاتف اختياري
        
        # تحويل الأرقام العربية إلى إنجليزية
        phone = format_english_number(phone.strip())
        
        # إزالة المسافات والرموز الشائعة
        phone = re.sub(r'[\s\-\(\)\+]', '', phone)
        
        # التحقق من أن الرقم يحتوي على أرقام فقط
        if not phone.isdigit():
            return False, "رقم الهاتف يجب أن يحتوي على أرقام فقط"
        
        # التحقق من طول رقم الهاتف
        if len(phone) < 7:
            return False, "رقم الهاتف قصير جداً"
        
        if len(phone) > 15:
            return False, "رقم الهاتف طويل جداً"
        
        return True, "صحيح"
    
    @staticmethod
    def validate_birth_date(birth_date):
        """التحقق من صحة تاريخ الميلاد"""
        if not birth_date or not birth_date.strip():
            return True, "صحيح"  # تاريخ الميلاد اختياري
        
        try:
            # محاولة تحليل التاريخ
            if '/' in birth_date:
                # تنسيق DD/MM/YYYY
                day, month, year = birth_date.split('/')
            elif '-' in birth_date:
                # تنسيق YYYY-MM-DD
                year, month, day = birth_date.split('-')
            else:
                return False, "تنسيق التاريخ غير صحيح (استخدم DD/MM/YYYY أو YYYY-MM-DD)"
            
            # تحويل إلى أرقام
            day = int(format_english_number(day))
            month = int(format_english_number(month))
            year = int(format_english_number(year))
            
            # التحقق من صحة التاريخ
            birth_date_obj = date(year, month, day)
            
            # التحقق من أن التاريخ ليس في المستقبل
            if birth_date_obj > date.today():
                return False, "تاريخ الميلاد لا يمكن أن يكون في المستقبل"
            
            # التحقق من أن العمر معقول (بين 15 و 80 سنة)
            age = (date.today() - birth_date_obj).days // 365
            if age < 15:
                return False, "العمر صغير جداً"
            
            if age > 80:
                return False, "العمر كبير جداً"
            
            return True, "صحيح"
            
        except ValueError:
            return False, "تاريخ الميلاد غير صحيح"
        except Exception as e:
            return False, f"خطأ في تاريخ الميلاد: {str(e)}"
    
    @staticmethod
    def validate_gender(gender):
        """التحقق من صحة الجنس"""
        if not gender or not gender.strip():
            return False, "الجنس مطلوب"
        
        valid_genders = ['ذكر', 'أنثى', 'male', 'female', 'م', 'أ']
        
        if gender.strip() not in valid_genders:
            return False, "الجنس يجب أن يكون 'ذكر' أو 'أنثى'"
        
        return True, "صحيح"
    
    @staticmethod
    def validate_section(section):
        """التحقق من صحة الشعبة"""
        if not section or not section.strip():
            return False, "الشعبة مطلوبة"
        
        section = section.strip()
        
        if len(section) < 2:
            return False, "اسم الشعبة قصير جداً"
        
        if len(section) > 50:
            return False, "اسم الشعبة طويل جداً"
        
        return True, "صحيح"
    
    @staticmethod
    def validate_grade(grade, max_grade=100, min_grade=0):
        """التحقق من صحة الدرجة"""
        if grade is None or grade == "":
            return True, "صحيح"  # الدرجة اختيارية
        
        try:
            # تحويل الأرقام العربية إلى إنجليزية
            grade_str = format_english_number(str(grade))
            grade_float = float(grade_str)
            
            if grade_float < min_grade:
                return False, f"الدرجة لا يمكن أن تكون أقل من {min_grade}"
            
            if grade_float > max_grade:
                return False, f"الدرجة لا يمكن أن تكون أكبر من {max_grade}"
            
            return True, "صحيح"
            
        except ValueError:
            return False, "الدرجة يجب أن تكون رقماً"
        except Exception as e:
            return False, f"خطأ في الدرجة: {str(e)}"
    
    @staticmethod
    def validate_academic_year(academic_year):
        """التحقق من صحة السنة الدراسية"""
        if not academic_year or not academic_year.strip():
            return False, "السنة الدراسية مطلوبة"
        
        # تنسيق السنة الدراسية: YYYY-YYYY
        pattern = r'^\d{4}-\d{4}$'
        if not re.match(pattern, academic_year.strip()):
            return False, "تنسيق السنة الدراسية غير صحيح (استخدم YYYY-YYYY)"
        
        try:
            start_year, end_year = academic_year.strip().split('-')
            start_year = int(start_year)
            end_year = int(end_year)
            
            if end_year != start_year + 1:
                return False, "السنة الدراسية يجب أن تكون متتالية"
            
            current_year = datetime.now().year
            if start_year < current_year - 10 or start_year > current_year + 5:
                return False, "السنة الدراسية غير معقولة"
            
            return True, "صحيح"
            
        except ValueError:
            return False, "السنة الدراسية غير صحيحة"
    
    @staticmethod
    def validate_email(email):
        """التحقق من صحة البريد الإلكتروني"""
        if not email or not email.strip():
            return True, "صحيح"  # البريد الإلكتروني اختياري
        
        email = email.strip()
        
        # نمط البريد الإلكتروني
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        
        if not re.match(pattern, email):
            return False, "تنسيق البريد الإلكتروني غير صحيح"
        
        return True, "صحيح"
    
    @staticmethod
    def validate_student_data(student_data):
        """التحقق من صحة بيانات الطالب كاملة"""
        errors = []
        
        # التحقق من الاسم
        is_valid, message = DataValidator.validate_name(student_data.get('full_name', ''))
        if not is_valid:
            errors.append(f"الاسم: {message}")
        
        # التحقق من الرقم الوطني
        is_valid, message = DataValidator.validate_national_id(student_data.get('national_id', ''))
        if not is_valid:
            errors.append(f"الرقم الوطني: {message}")
        
        # التحقق من الشعبة
        is_valid, message = DataValidator.validate_section(student_data.get('section', ''))
        if not is_valid:
            errors.append(f"الشعبة: {message}")
        
        # التحقق من الجنس
        is_valid, message = DataValidator.validate_gender(student_data.get('gender', ''))
        if not is_valid:
            errors.append(f"الجنس: {message}")
        
        # التحقق من تاريخ الميلاد
        is_valid, message = DataValidator.validate_birth_date(student_data.get('birth_date', ''))
        if not is_valid:
            errors.append(f"تاريخ الميلاد: {message}")
        
        # التحقق من رقم الهاتف
        is_valid, message = DataValidator.validate_phone(student_data.get('phone', ''))
        if not is_valid:
            errors.append(f"رقم الهاتف: {message}")
        
        # التحقق من رقم هاتف ولي الأمر
        is_valid, message = DataValidator.validate_phone(student_data.get('guardian_phone', ''))
        if not is_valid:
            errors.append(f"رقم هاتف ولي الأمر: {message}")
        
        if errors:
            return False, errors
        else:
            return True, "جميع البيانات صحيحة"
    
    @staticmethod
    def validate_grade_data(grade_data, max_grade=100):
        """التحقق من صحة بيانات الدرجات"""
        errors = []
        
        grade_fields = [
            ('semester1_work', 'أعمال الفصل الأول'),
            ('semester1_exam', 'امتحان الفصل الأول'),
            ('semester2_work', 'أعمال الفصل الثاني'),
            ('semester2_exam', 'امتحان الفصل الثاني'),
            ('second_round_exam', 'امتحان الدور الثاني')
        ]
        
        for field, field_name in grade_fields:
            if field in grade_data:
                is_valid, message = DataValidator.validate_grade(
                    grade_data[field], max_grade
                )
                if not is_valid:
                    errors.append(f"{field_name}: {message}")
        
        if errors:
            return False, errors
        else:
            return True, "جميع الدرجات صحيحة"

# دوال مساعدة سريعة
def is_valid_name(name):
    """التحقق السريع من صحة الاسم"""
    return DataValidator.validate_name(name)[0]

def is_valid_national_id(national_id):
    """التحقق السريع من صحة الرقم الوطني"""
    return DataValidator.validate_national_id(national_id)[0]

def is_valid_phone(phone):
    """التحقق السريع من صحة رقم الهاتف"""
    return DataValidator.validate_phone(phone)[0]

def is_valid_grade(grade, max_grade=100):
    """التحقق السريع من صحة الدرجة"""
    return DataValidator.validate_grade(grade, max_grade)[0]
