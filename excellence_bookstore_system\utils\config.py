#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات التطبيق - نظام محاسبة مكتبة التميز
Application Configuration for Excellence Bookstore System

المنظومة الإلكترونية - إعداد محمد مطرود
"""

import json
import os
from datetime import datetime

class AppConfig:
    """فئة إدارة إعدادات التطبيق"""
    
    def __init__(self):
        """تهيئة الإعدادات"""
        self.config_file = os.path.join(
            os.path.dirname(os.path.dirname(__file__)), 
            "data", 
            "app_config.json"
        )
        
        # الإعدادات الافتراضية
        self.default_config = {
            "store_info": {
                "name": "مكتبة التميز",
                "address": "العنوان",
                "phone": "0123456789",
                "email": "<EMAIL>",
                "logo_path": "",
                "tax_number": "",
                "commercial_register": ""
            },
            "system_info": {
                "version": "1.0",
                "developer": "المنظومة الإلكترونية - إعداد محمد مطرود",
                "last_backup": "",
                "database_version": "1.0"
            },
            "invoice_settings": {
                "prefix": "INV",
                "auto_print": False,
                "default_payment_method": "نقدي",
                "show_customer_info": True,
                "show_tax": False,
                "tax_rate": 0.0
            },
            "ui_settings": {
                "theme": "default",
                "font_size": 11,
                "window_size": {
                    "width": 1400,
                    "height": 900
                },
                "language": "arabic",
                "rtl_support": True
            },
            "printing_settings": {
                "default_printer": "",
                "paper_size": "A4",
                "margins": {
                    "top": 20,
                    "bottom": 20,
                    "left": 20,
                    "right": 20
                },
                "header_height": 100,
                "footer_height": 50
            },
            "backup_settings": {
                "auto_backup": True,
                "backup_interval": 7,  # أيام
                "backup_location": "backups",
                "max_backups": 30
            },
            "security_settings": {
                "require_password": False,
                "session_timeout": 0,  # 0 = لا يوجد انتهاء صلاحية
                "log_activities": True
            },
            "currency_settings": {
                "currency": "ريال",
                "currency_symbol": "ر.س",
                "decimal_places": 2,
                "thousands_separator": ","
            }
        }
        
        # تحميل الإعدادات
        self.config = self.load_config()
    
    def load_config(self):
        """تحميل الإعدادات من الملف"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                
                # دمج الإعدادات المحملة مع الافتراضية
                config = self.default_config.copy()
                self._merge_config(config, loaded_config)
                return config
            else:
                # إنشاء ملف الإعدادات الافتراضي
                self.save_config(self.default_config)
                return self.default_config.copy()
                
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
            return self.default_config.copy()
    
    def _merge_config(self, default, loaded):
        """دمج الإعدادات المحملة مع الافتراضية"""
        for key, value in loaded.items():
            if key in default:
                if isinstance(value, dict) and isinstance(default[key], dict):
                    self._merge_config(default[key], value)
                else:
                    default[key] = value
    
    def save_config(self, config=None):
        """حفظ الإعدادات في الملف"""
        try:
            if config is None:
                config = self.config
            
            # إنشاء مجلد البيانات إذا لم يكن موجوداً
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            # إضافة معلومات الحفظ
            config['system_info']['last_updated'] = datetime.now().isoformat()
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            
            return True
            
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")
            return False
    
    def get(self, key_path, default=None):
        """الحصول على قيمة إعداد باستخدام مسار النقاط"""
        try:
            keys = key_path.split('.')
            value = self.config
            
            for key in keys:
                value = value[key]
            
            return value
            
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path, value):
        """تعيين قيمة إعداد باستخدام مسار النقاط"""
        try:
            keys = key_path.split('.')
            config = self.config
            
            # الانتقال إلى المستوى الأخير
            for key in keys[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
            
            # تعيين القيمة
            config[keys[-1]] = value
            return True
            
        except Exception as e:
            print(f"خطأ في تعيين الإعداد {key_path}: {e}")
            return False
    
    def reset_to_default(self):
        """إعادة تعيين الإعدادات إلى القيم الافتراضية"""
        self.config = self.default_config.copy()
        return self.save_config()
    
    def export_config(self, file_path):
        """تصدير الإعدادات إلى ملف"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"خطأ في تصدير الإعدادات: {e}")
            return False
    
    def import_config(self, file_path):
        """استيراد الإعدادات من ملف"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # دمج الإعدادات المستوردة
            self._merge_config(self.config, imported_config)
            return self.save_config()
            
        except Exception as e:
            print(f"خطأ في استيراد الإعدادات: {e}")
            return False
    
    # خصائص سريعة للوصول للإعدادات الشائعة
    @property
    def store_name(self):
        return self.get('store_info.name', 'مكتبة التميز')
    
    @property
    def store_address(self):
        return self.get('store_info.address', 'العنوان')
    
    @property
    def store_phone(self):
        return self.get('store_info.phone', '0123456789')
    
    @property
    def store_email(self):
        return self.get('store_info.email', '<EMAIL>')
    
    @property
    def developer_info(self):
        return self.get('system_info.developer', 'المنظومة الإلكترونية - إعداد محمد مطرود')
    
    @property
    def invoice_prefix(self):
        return self.get('invoice_settings.prefix', 'INV')
    
    @property
    def currency(self):
        return self.get('currency_settings.currency', 'ريال')
    
    @property
    def currency_symbol(self):
        return self.get('currency_settings.currency_symbol', 'ر.س')
    
    @property
    def font_size(self):
        return self.get('ui_settings.font_size', 11)
    
    @property
    def tax_rate(self):
        return self.get('invoice_settings.tax_rate', 0.0)
    
    def get_window_size(self):
        """الحصول على حجم النافذة"""
        width = self.get('ui_settings.window_size.width', 1400)
        height = self.get('ui_settings.window_size.height', 900)
        return width, height
    
    def set_window_size(self, width, height):
        """تعيين حجم النافذة"""
        self.set('ui_settings.window_size.width', width)
        self.set('ui_settings.window_size.height', height)
    
    def format_currency(self, amount):
        """تنسيق المبلغ بالعملة"""
        try:
            decimal_places = self.get('currency_settings.decimal_places', 2)
            thousands_sep = self.get('currency_settings.thousands_separator', ',')
            currency = self.currency
            
            # تنسيق الرقم
            formatted_amount = f"{float(amount):,.{decimal_places}f}"
            
            # استبدال الفاصلة الافتراضية بالفاصلة المحددة
            if thousands_sep != ',':
                formatted_amount = formatted_amount.replace(',', thousands_sep)
            
            return f"{formatted_amount} {currency}"
        except:
            return f"0.00 {self.currency}"
    
    def get_print_margins(self):
        """الحصول على هوامش الطباعة"""
        return {
            'top': self.get('printing_settings.margins.top', 20),
            'bottom': self.get('printing_settings.margins.bottom', 20),
            'left': self.get('printing_settings.margins.left', 20),
            'right': self.get('printing_settings.margins.right', 20)
        }
    
    def should_auto_backup(self):
        """التحقق من ضرورة النسخ الاحتياطي التلقائي"""
        return self.get('backup_settings.auto_backup', True)
    
    def get_backup_interval(self):
        """الحصول على فترة النسخ الاحتياطي بالأيام"""
        return self.get('backup_settings.backup_interval', 7)
