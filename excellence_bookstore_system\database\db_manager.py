#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات - نظام محاسبة مكتبة التميز
Database Manager for Excellence Bookstore System

المنظومة الإلكترونية - إعداد محمد مطرود
"""

import sqlite3
import os
import shutil
from datetime import datetime, date
import json
import threading

class DatabaseManager:
    """مدير قاعدة البيانات المتطور"""
    
    def __init__(self, db_path="excellence_bookstore.db"):
        """تهيئة مدير قاعدة البيانات"""
        self.db_path = db_path
        self.connection = None
        self.lock = threading.Lock()
        
        # إنشاء قاعدة البيانات والجداول
        self.init_database()
        
        # إعداد النسخ الاحتياطي التلقائي
        self.setup_auto_backup()
    
    def get_connection(self):
        """الحصول على اتصال آمن بقاعدة البيانات"""
        with self.lock:
            if self.connection is None:
                self.connection = sqlite3.connect(
                    self.db_path, 
                    check_same_thread=False,
                    timeout=30.0
                )
                self.connection.row_factory = sqlite3.Row
                # تفعيل المفاتيح الخارجية
                self.connection.execute("PRAGMA foreign_keys = ON")
            return self.connection
    
    def close_connection(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        with self.lock:
            if self.connection:
                self.connection.close()
                self.connection = None
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # تفعيل المفاتيح الخارجية
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # جدول الأصناف والمنتجات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                category TEXT DEFAULT 'عام',
                default_price REAL DEFAULT 0.0,
                is_variable_price BOOLEAN DEFAULT 0,
                description TEXT,
                barcode TEXT UNIQUE,
                status TEXT DEFAULT 'نشط' CHECK(status IN ('نشط', 'غير نشط')),
                created_date TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_date TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الفواتير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                date TEXT NOT NULL,
                customer_name TEXT DEFAULT 'عميل نقدي',
                customer_email TEXT,
                customer_phone TEXT,
                subtotal REAL DEFAULT 0.0,
                discount REAL DEFAULT 0.0,
                total REAL DEFAULT 0.0,
                payment_method TEXT DEFAULT 'نقدي' CHECK(payment_method IN ('نقدي', 'بطاقة', 'آجلة')),
                paid_amount REAL DEFAULT 0.0,
                remaining_amount REAL DEFAULT 0.0,
                status TEXT DEFAULT 'مكتملة' CHECK(status IN ('مكتملة', 'مديونية', 'ملغية')),
                notes TEXT,
                created_by TEXT DEFAULT 'النظام',
                created_date TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول تفاصيل الفواتير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoice_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER NOT NULL,
                product_id INTEGER,
                product_name TEXT NOT NULL,
                quantity REAL NOT NULL DEFAULT 1,
                unit_price REAL NOT NULL DEFAULT 0.0,
                total_price REAL NOT NULL DEFAULT 0.0,
                notes TEXT,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE SET NULL
            )
        ''')
        
        # جدول المصاريف
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS expenses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                description TEXT NOT NULL,
                amount REAL NOT NULL DEFAULT 0.0,
                date TEXT NOT NULL,
                category TEXT DEFAULT 'عام',
                payment_method TEXT DEFAULT 'نقدي',
                notes TEXT,
                created_by TEXT DEFAULT 'النظام',
                created_date TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الديون
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS debts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER NOT NULL,
                customer_name TEXT NOT NULL,
                customer_phone TEXT,
                customer_email TEXT,
                total_amount REAL NOT NULL DEFAULT 0.0,
                paid_amount REAL DEFAULT 0.0,
                remaining_amount REAL NOT NULL DEFAULT 0.0,
                status TEXT DEFAULT 'مستحق' CHECK(status IN ('مستحق', 'مسدد جزئياً', 'مسدد بالكامل', 'متأخر')),
                due_date TEXT,
                created_date TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE
            )
        ''')
        
        # جدول دفعات الديون
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS debt_payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                debt_id INTEGER NOT NULL,
                amount REAL NOT NULL DEFAULT 0.0,
                payment_date TEXT NOT NULL,
                payment_method TEXT DEFAULT 'نقدي',
                notes TEXT,
                created_by TEXT DEFAULT 'النظام',
                created_date TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (debt_id) REFERENCES debts (id) ON DELETE CASCADE
            )
        ''')
        
        # جدول الإعدادات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT,
                description TEXT,
                updated_date TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول النسخ الاحتياطية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS backups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                backup_name TEXT NOT NULL,
                backup_path TEXT NOT NULL,
                backup_size INTEGER,
                backup_date TEXT DEFAULT CURRENT_TIMESTAMP,
                backup_type TEXT DEFAULT 'يدوي' CHECK(backup_type IN ('يدوي', 'تلقائي')),
                description TEXT
            )
        ''')
        
        # جدول سجل العمليات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS activity_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                action_type TEXT NOT NULL,
                table_name TEXT,
                record_id INTEGER,
                old_values TEXT,
                new_values TEXT,
                user_name TEXT DEFAULT 'النظام',
                timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                ip_address TEXT
            )
        ''')
        
        # إنشاء الفهارس لتحسين الأداء
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_invoices_date ON invoices(date)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_invoices_number ON invoices(invoice_number)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_invoices_customer ON invoices(customer_name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_invoice_items_invoice_id ON invoice_items(invoice_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(date)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_debts_status ON debts(status)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_products_name ON products(name)')
        
        # إنشاء المشاهد (Views) للاستعلامات المعقدة
        cursor.execute('''
            CREATE VIEW IF NOT EXISTS invoice_summary AS
            SELECT 
                i.id,
                i.invoice_number,
                i.date,
                i.customer_name,
                i.total,
                i.paid_amount,
                i.remaining_amount,
                i.status,
                i.payment_method,
                COUNT(ii.id) as items_count
            FROM invoices i
            LEFT JOIN invoice_items ii ON i.id = ii.invoice_id
            GROUP BY i.id
        ''')
        
        cursor.execute('''
            CREATE VIEW IF NOT EXISTS daily_sales_summary AS
            SELECT 
                date,
                COUNT(*) as invoices_count,
                SUM(total) as total_sales,
                SUM(paid_amount) as total_paid,
                SUM(remaining_amount) as total_remaining
            FROM invoices
            WHERE status != 'ملغية'
            GROUP BY date
            ORDER BY date DESC
        ''')
        
        conn.commit()
        conn.close()
        
        # إضافة بيانات افتراضية
        self.add_default_data()
    
    def add_default_data(self):
        """إضافة بيانات افتراضية للنظام"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # إضافة إعدادات افتراضية
            default_settings = [
                ('store_name', 'مكتبة التميز', 'اسم المكتبة'),
                ('store_address', 'العنوان', 'عنوان المكتبة'),
                ('store_phone', '0123456789', 'رقم هاتف المكتبة'),
                ('store_email', '<EMAIL>', 'البريد الإلكتروني'),
                ('tax_rate', '0.0', 'معدل الضريبة'),
                ('currency', 'ريال', 'العملة المستخدمة'),
                ('invoice_prefix', 'INV', 'بادئة رقم الفاتورة'),
                ('backup_interval', '7', 'فترة النسخ الاحتياطي بالأيام'),
                ('system_version', '1.0', 'إصدار النظام'),
                ('developer_info', 'المنظومة الإلكترونية - إعداد محمد مطرود', 'معلومات المطور')
            ]
            
            for key, value, description in default_settings:
                cursor.execute('''
                    INSERT OR IGNORE INTO settings (key, value, description) 
                    VALUES (?, ?, ?)
                ''', (key, value, description))
            
            # إضافة أصناف افتراضية
            default_products = [
                ('أقلام رصاص', 'قرطاسية', 2.0, 0, 'أقلام رصاص عادية'),
                ('أقلام حبر', 'قرطاسية', 5.0, 0, 'أقلام حبر زرقاء وسوداء'),
                ('دفاتر مدرسية', 'قرطاسية', 10.0, 0, 'دفاتر 100 ورقة'),
                ('كراسات رسم', 'قرطاسية', 15.0, 0, 'كراسات رسم A4'),
                ('مساطر', 'قرطاسية', 3.0, 0, 'مساطر بلاستيكية 30 سم'),
                ('تأمين سيارة', 'تأمين', 0.0, 1, 'تأمين سيارات بسعر متغير'),
                ('خدمات متنوعة', 'خدمات', 0.0, 1, 'خدمات بأسعار متغيرة')
            ]
            
            for name, category, price, is_variable, description in default_products:
                cursor.execute('''
                    INSERT OR IGNORE INTO products 
                    (name, category, default_price, is_variable_price, description) 
                    VALUES (?, ?, ?, ?, ?)
                ''', (name, category, price, is_variable, description))
            
            conn.commit()
            print("تم إضافة البيانات الافتراضية بنجاح")
            
        except Exception as e:
            print(f"خطأ في إضافة البيانات الافتراضية: {e}")
            conn.rollback()
        finally:
            conn.close()
    
    def setup_auto_backup(self):
        """إعداد النسخ الاحتياطي التلقائي"""
        try:
            backup_dir = os.path.join(os.path.dirname(self.db_path), "backups")
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)
        except Exception as e:
            print(f"خطأ في إعداد مجلد النسخ الاحتياطي: {e}")
    
    def create_backup(self, backup_name=None, description=""):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            if backup_name is None:
                backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            
            backup_dir = os.path.join(os.path.dirname(self.db_path), "backups")
            backup_path = os.path.join(backup_dir, backup_name)
            
            # نسخ قاعدة البيانات
            shutil.copy2(self.db_path, backup_path)
            
            # تسجيل النسخة الاحتياطية
            backup_size = os.path.getsize(backup_path)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO backups (backup_name, backup_path, backup_size, description)
                VALUES (?, ?, ?, ?)
            ''', (backup_name, backup_path, backup_size, description))
            
            conn.commit()
            conn.close()
            
            return True, backup_path
            
        except Exception as e:
            return False, str(e)
    
    def log_activity(self, action_type, table_name=None, record_id=None, 
                    old_values=None, new_values=None, user_name="النظام"):
        """تسجيل العمليات في سجل النشاط"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO activity_log 
                (action_type, table_name, record_id, old_values, new_values, user_name)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                action_type, table_name, record_id,
                json.dumps(old_values, ensure_ascii=False) if old_values else None,
                json.dumps(new_values, ensure_ascii=False) if new_values else None,
                user_name
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في تسجيل النشاط: {e}")
    
    def get_next_invoice_number(self):
        """الحصول على رقم الفاتورة التالي"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # الحصول على بادئة الفاتورة
            cursor.execute("SELECT value FROM settings WHERE key = 'invoice_prefix'")
            prefix_result = cursor.fetchone()
            prefix = prefix_result[0] if prefix_result else "INV"
            
            # الحصول على آخر رقم فاتورة
            cursor.execute('''
                SELECT invoice_number FROM invoices 
                WHERE invoice_number LIKE ? 
                ORDER BY id DESC LIMIT 1
            ''', (f"{prefix}%",))
            
            last_invoice = cursor.fetchone()
            
            if last_invoice:
                # استخراج الرقم من آخر فاتورة
                last_number = last_invoice[0].replace(prefix, "")
                try:
                    next_number = int(last_number) + 1
                except ValueError:
                    next_number = 1
            else:
                next_number = 1
            
            conn.close()
            
            # تكوين رقم الفاتورة الجديد
            return f"{prefix}{next_number:06d}"
            
        except Exception as e:
            print(f"خطأ في توليد رقم الفاتورة: {e}")
            return f"INV{datetime.now().strftime('%Y%m%d%H%M%S')}"

    # وظائف إدارة المنتجات
    def add_product(self, product_data):
        """إضافة منتج جديد"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO products (name, category, default_price, is_variable_price, description, barcode)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                product_data['name'],
                product_data.get('category', 'عام'),
                product_data.get('default_price', 0.0),
                product_data.get('is_variable_price', 0),
                product_data.get('description', ''),
                product_data.get('barcode', '')
            ))

            product_id = cursor.lastrowid
            conn.commit()
            conn.close()

            # تسجيل العملية
            self.log_activity("إضافة منتج", "products", product_id, None, product_data)

            return product_id

        except Exception as e:
            raise Exception(f"خطأ في إضافة المنتج: {str(e)}")

    def get_all_products(self, active_only=True):
        """الحصول على جميع المنتجات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            query = "SELECT * FROM products"
            if active_only:
                query += " WHERE status = 'نشط'"
            query += " ORDER BY name"

            cursor.execute(query)
            results = cursor.fetchall()
            conn.close()

            return results

        except Exception as e:
            print(f"خطأ في الحصول على المنتجات: {e}")
            return []

    def search_products(self, search_term):
        """البحث عن المنتجات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM products
                WHERE (name LIKE ? OR barcode LIKE ? OR description LIKE ?)
                AND status = 'نشط'
                ORDER BY name
            ''', (f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'))

            results = cursor.fetchall()
            conn.close()

            return results

        except Exception as e:
            print(f"خطأ في البحث عن المنتجات: {e}")
            return []

    # وظائف إدارة الفواتير
    def create_invoice(self, invoice_data, items_data):
        """إنشاء فاتورة جديدة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إنشاء الفاتورة
            cursor.execute('''
                INSERT INTO invoices (
                    invoice_number, date, customer_name, customer_email, customer_phone,
                    subtotal, discount, total, payment_method, paid_amount, remaining_amount, status, notes
                )
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                invoice_data['invoice_number'],
                invoice_data['date'],
                invoice_data.get('customer_name', 'عميل نقدي'),
                invoice_data.get('customer_email', ''),
                invoice_data.get('customer_phone', ''),
                invoice_data['subtotal'],
                invoice_data.get('discount', 0.0),
                invoice_data['total'],
                invoice_data.get('payment_method', 'نقدي'),
                invoice_data['paid_amount'],
                invoice_data['remaining_amount'],
                invoice_data['status'],
                invoice_data.get('notes', '')
            ))

            invoice_id = cursor.lastrowid

            # إضافة عناصر الفاتورة
            for item in items_data:
                cursor.execute('''
                    INSERT INTO invoice_items (
                        invoice_id, product_id, product_name, quantity, unit_price, total_price, notes
                    )
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    invoice_id,
                    item.get('product_id'),
                    item['product_name'],
                    item['quantity'],
                    item['unit_price'],
                    item['total_price'],
                    item.get('notes', '')
                ))

            # إنشاء دين إذا كان هناك مبلغ متبقي
            if invoice_data['remaining_amount'] > 0:
                cursor.execute('''
                    INSERT INTO debts (
                        invoice_id, customer_name, customer_phone, customer_email,
                        total_amount, paid_amount, remaining_amount, status
                    )
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    invoice_id,
                    invoice_data.get('customer_name', 'عميل نقدي'),
                    invoice_data.get('customer_phone', ''),
                    invoice_data.get('customer_email', ''),
                    invoice_data['total'],
                    invoice_data['paid_amount'],
                    invoice_data['remaining_amount'],
                    'مستحق'
                ))

            conn.commit()
            conn.close()

            # تسجيل العملية
            self.log_activity("إنشاء فاتورة", "invoices", invoice_id, None, invoice_data)

            return invoice_id

        except Exception as e:
            conn.rollback()
            conn.close()
            raise Exception(f"خطأ في إنشاء الفاتورة: {str(e)}")

    def get_invoice_by_id(self, invoice_id):
        """الحصول على فاتورة بالمعرف"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # الحصول على بيانات الفاتورة
            cursor.execute("SELECT * FROM invoices WHERE id = ?", (invoice_id,))
            invoice = cursor.fetchone()

            if not invoice:
                return None

            # الحصول على عناصر الفاتورة
            cursor.execute("SELECT * FROM invoice_items WHERE invoice_id = ?", (invoice_id,))
            items = cursor.fetchall()

            conn.close()

            return {
                'invoice': dict(invoice),
                'items': [dict(item) for item in items]
            }

        except Exception as e:
            print(f"خطأ في الحصول على الفاتورة: {e}")
            return None

    def get_invoices_by_date_range(self, start_date, end_date):
        """الحصول على الفواتير في فترة زمنية"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM invoice_summary
                WHERE date BETWEEN ? AND ?
                ORDER BY date DESC, id DESC
            ''', (start_date, end_date))

            results = cursor.fetchall()
            conn.close()

            return [dict(row) for row in results]

        except Exception as e:
            print(f"خطأ في الحصول على الفواتير: {e}")
            return []

    # وظائف إدارة المصاريف
    def add_expense(self, expense_data):
        """إضافة مصروف جديد"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO expenses (description, amount, date, category, payment_method, notes)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                expense_data['description'],
                expense_data['amount'],
                expense_data['date'],
                expense_data.get('category', 'عام'),
                expense_data.get('payment_method', 'نقدي'),
                expense_data.get('notes', '')
            ))

            expense_id = cursor.lastrowid
            conn.commit()
            conn.close()

            # تسجيل العملية
            self.log_activity("إضافة مصروف", "expenses", expense_id, None, expense_data)

            return expense_id

        except Exception as e:
            raise Exception(f"خطأ في إضافة المصروف: {str(e)}")

    def get_expenses_by_date_range(self, start_date, end_date):
        """الحصول على المصاريف في فترة زمنية"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM expenses
                WHERE date BETWEEN ? AND ?
                ORDER BY date DESC, id DESC
            ''', (start_date, end_date))

            results = cursor.fetchall()
            conn.close()

            return [dict(row) for row in results]

        except Exception as e:
            print(f"خطأ في الحصول على المصاريف: {e}")
            return []

    # وظائف إدارة الديون
    def get_all_debts(self, status_filter=None):
        """الحصول على جميع الديون"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            query = '''
                SELECT d.*, i.invoice_number, i.date as invoice_date
                FROM debts d
                JOIN invoices i ON d.invoice_id = i.id
            '''

            if status_filter:
                query += f" WHERE d.status = '{status_filter}'"

            query += " ORDER BY d.created_date DESC"

            cursor.execute(query)
            results = cursor.fetchall()
            conn.close()

            return [dict(row) for row in results]

        except Exception as e:
            print(f"خطأ في الحصول على الديون: {e}")
            return []

    def add_debt_payment(self, debt_id, payment_amount, payment_method='نقدي', notes=''):
        """إضافة دفعة للدين"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # الحصول على بيانات الدين الحالية
            cursor.execute("SELECT * FROM debts WHERE id = ?", (debt_id,))
            debt = cursor.fetchone()

            if not debt:
                raise Exception("الدين غير موجود")

            # حساب المبلغ الجديد
            new_paid_amount = debt[5] + payment_amount  # paid_amount
            new_remaining_amount = debt[4] - new_paid_amount  # total_amount - new_paid_amount

            # تحديد الحالة الجديدة
            if new_remaining_amount <= 0:
                new_status = 'مسدد بالكامل'
                new_remaining_amount = 0
            elif new_paid_amount > 0:
                new_status = 'مسدد جزئياً'
            else:
                new_status = 'مستحق'

            # إضافة الدفعة
            cursor.execute('''
                INSERT INTO debt_payments (debt_id, amount, payment_date, payment_method, notes)
                VALUES (?, ?, ?, ?, ?)
            ''', (debt_id, payment_amount, datetime.now().strftime('%Y-%m-%d'), payment_method, notes))

            # تحديث الدين
            cursor.execute('''
                UPDATE debts
                SET paid_amount = ?, remaining_amount = ?, status = ?
                WHERE id = ?
            ''', (new_paid_amount, new_remaining_amount, new_status, debt_id))

            # تحديث الفاتورة
            cursor.execute('''
                UPDATE invoices
                SET paid_amount = ?, remaining_amount = ?, status = ?
                WHERE id = ?
            ''', (new_paid_amount, new_remaining_amount,
                  'مكتملة' if new_status == 'مسدد بالكامل' else 'مديونية', debt[1]))

            conn.commit()
            conn.close()

            # تسجيل العملية
            self.log_activity("دفعة دين", "debt_payments", debt_id, None, {
                'amount': payment_amount,
                'payment_method': payment_method
            })

            return True

        except Exception as e:
            conn.rollback()
            conn.close()
            raise Exception(f"خطأ في إضافة دفعة الدين: {str(e)}")

    # وظائف التقارير
    def get_daily_sales_report(self, date):
        """تقرير المبيعات اليومية"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM daily_sales_summary WHERE date = ?
            ''', (date,))

            result = cursor.fetchone()
            conn.close()

            return dict(result) if result else None

        except Exception as e:
            print(f"خطأ في تقرير المبيعات اليومية: {e}")
            return None

    def get_monthly_profit_report(self, year, month):
        """تقرير الأرباح الشهرية"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # المبيعات الشهرية
            cursor.execute('''
                SELECT SUM(total) as total_sales, SUM(paid_amount) as total_paid
                FROM invoices
                WHERE strftime('%Y', date) = ? AND strftime('%m', date) = ?
                AND status != 'ملغية'
            ''', (str(year), f"{month:02d}"))

            sales_result = cursor.fetchone()

            # المصاريف الشهرية
            cursor.execute('''
                SELECT SUM(amount) as total_expenses
                FROM expenses
                WHERE strftime('%Y', date) = ? AND strftime('%m', date) = ?
            ''', (str(year), f"{month:02d}"))

            expenses_result = cursor.fetchone()

            conn.close()

            total_sales = sales_result[0] if sales_result[0] else 0
            total_paid = sales_result[1] if sales_result[1] else 0
            total_expenses = expenses_result[0] if expenses_result[0] else 0

            profit = total_paid - total_expenses

            return {
                'total_sales': total_sales,
                'total_paid': total_paid,
                'total_expenses': total_expenses,
                'profit': profit,
                'year': year,
                'month': month
            }

        except Exception as e:
            print(f"خطأ في تقرير الأرباح الشهرية: {e}")
            return None
