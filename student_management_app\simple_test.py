#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للمكونات الأساسية
Simple Component Test
"""

import tkinter as tk
import sys
import os

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_imports():
    """اختبار استيراد المكونات"""
    print("اختبار استيراد المكونات...")
    
    try:
        # اختبار استيراد الإعدادات
        from config.settings import AppSettings
        print("✓ تم استيراد الإعدادات")
        
        # اختبار استيراد قاعدة البيانات
        from database.db_manager import DatabaseManager
        print("✓ تم استيراد مدير قاعدة البيانات")
        
        # اختبار استيراد الدعم العربي
        from utils.arabic_support import setup_arabic_font, get_arabic_font
        print("✓ تم استيراد الدعم العربي")
        
        # اختبار استيراد المساعدات
        from utils.helpers import show_success_message
        print("✓ تم استيراد المساعدات")
        
        # اختبار استيراد التحقق
        from utils.validators import DataValidator
        print("✓ تم استيراد أدوات التحقق")
        
        return True
        
    except ImportError as e:
        print(f"✗ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"✗ خطأ عام: {e}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("\nاختبار قاعدة البيانات...")
    
    try:
        from database.db_manager import DatabaseManager
        
        # إنشاء قاعدة بيانات اختبار
        db_path = os.path.join(current_dir, "data", "test.db")
        db = DatabaseManager(db_path)
        print("✓ تم إنشاء قاعدة البيانات")
        
        # اختبار الحصول على الشعب
        sections = db.get_all_sections()
        print(f"✓ تم الحصول على {len(sections)} شعبة")
        
        # اختبار الحصول على المواد
        subjects = db.get_all_subjects()
        print(f"✓ تم الحصول على {len(subjects)} مادة")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في قاعدة البيانات: {e}")
        return False

def test_gui():
    """اختبار واجهة المستخدم الأساسية"""
    print("\nاختبار واجهة المستخدم...")
    
    try:
        from utils.arabic_support import setup_arabic_font, get_arabic_font, create_arabic_label
        
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.title("اختبار واجهة المستخدم")
        root.geometry("400x300")
        
        # إعداد الخط العربي
        setup_arabic_font(root)
        print("✓ تم إعداد الخط العربي")
        
        # إنشاء تسمية عربية
        label = create_arabic_label(root, "مرحباً بك في نظام إدارة الطلاب")
        label.pack(pady=20)
        print("✓ تم إنشاء تسمية عربية")
        
        # زر الإغلاق
        close_btn = tk.Button(
            root,
            text="إغلاق",
            font=get_arabic_font(size=12),
            command=root.destroy
        )
        close_btn.pack(pady=10)
        
        print("✓ تم إنشاء واجهة المستخدم الأساسية")
        print("سيتم عرض النافذة لمدة 3 ثوانٍ...")
        
        # عرض النافذة لفترة قصيرة
        root.after(3000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في واجهة المستخدم: {e}")
        return False

def test_settings():
    """اختبار الإعدادات"""
    print("\nاختبار الإعدادات...")
    
    try:
        from config.settings import AppSettings
        
        # إنشاء إعدادات
        settings = AppSettings()
        print("✓ تم إنشاء الإعدادات")
        
        # اختبار الحصول على القيم
        institute_name = settings.institute_name
        print(f"✓ اسم المعهد: {institute_name}")
        
        academic_year = settings.current_academic_year
        print(f"✓ السنة الدراسية: {academic_year}")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في الإعدادات: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 50)
    print("اختبار نظام إدارة الطلاب")
    print("=" * 50)
    
    tests = [
        ("استيراد المكونات", test_imports),
        ("قاعدة البيانات", test_database),
        ("الإعدادات", test_settings),
        ("واجهة المستخدم", test_gui),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✓ نجح اختبار {test_name}")
            else:
                print(f"✗ فشل اختبار {test_name}")
        except Exception as e:
            print(f"✗ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 50)
    print(f"نتائج الاختبار: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        print("التطبيق جاهز للتشغيل")
    else:
        print("⚠️ بعض الاختبارات فشلت")
        print("يرجى مراجعة الأخطاء وإصلاحها")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
