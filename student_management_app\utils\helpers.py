#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
دوال مساعدة عامة
General Helper Functions

معهد المتوسط للمهن الشاملة - اجخرة
"""

import os
import json
import csv
import shutil
from datetime import datetime, timedelta
from tkinter import messagebox, filedialog
import tkinter as tk

def show_success_message(title, message):
    """عرض رسالة نجاح"""
    messagebox.showinfo(title, message)

def show_error_message(title, message):
    """عرض رسالة خطأ"""
    messagebox.showerror(title, message)

def show_warning_message(title, message):
    """عرض رسالة تحذير"""
    messagebox.showwarning(title, message)

def ask_yes_no(title, message):
    """طرح سؤال نعم/لا"""
    return messagebox.askyesno(title, message)

def ask_ok_cancel(title, message):
    """طرح سؤال موافق/إلغاء"""
    return messagebox.askokcancel(title, message)

def get_current_academic_year():
    """الحصول على السنة الدراسية الحالية"""
    current_date = datetime.now()
    
    # إذا كان الشهر من سبتمبر إلى ديسمبر، فالسنة الدراسية تبدأ من السنة الحالية
    if current_date.month >= 9:
        start_year = current_date.year
        end_year = current_date.year + 1
    else:
        # إذا كان الشهر من يناير إلى أغسطس، فالسنة الدراسية بدأت من السنة الماضية
        start_year = current_date.year - 1
        end_year = current_date.year
    
    return f"{start_year}-{end_year}"

def format_file_size(size_bytes):
    """تنسيق حجم الملف"""
    if size_bytes == 0:
        return "0 بايت"
    
    size_names = ["بايت", "كيلوبايت", "ميجابايت", "جيجابايت"]
    i = 0
    
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"

def create_backup_filename(prefix="backup"):
    """إنشاء اسم ملف نسخة احتياطية"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"{prefix}_{timestamp}.db"

def ensure_directory_exists(directory_path):
    """التأكد من وجود المجلد وإنشاؤه إذا لم يكن موجوداً"""
    try:
        if not os.path.exists(directory_path):
            os.makedirs(directory_path)
            return True
        return True
    except Exception as e:
        print(f"خطأ في إنشاء المجلد {directory_path}: {e}")
        return False

def safe_file_copy(source, destination):
    """نسخ ملف بأمان"""
    try:
        # التأكد من وجود مجلد الوجهة
        dest_dir = os.path.dirname(destination)
        ensure_directory_exists(dest_dir)
        
        # نسخ الملف
        shutil.copy2(source, destination)
        return True, "تم نسخ الملف بنجاح"
    except Exception as e:
        return False, f"خطأ في نسخ الملف: {str(e)}"

def export_to_csv(data, filename, headers=None):
    """تصدير البيانات إلى ملف CSV"""
    try:
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)
            
            # كتابة العناوين إذا كانت متوفرة
            if headers:
                writer.writerow(headers)
            
            # كتابة البيانات
            for row in data:
                writer.writerow(row)
        
        return True, f"تم تصدير البيانات إلى {filename}"
    except Exception as e:
        return False, f"خطأ في تصدير البيانات: {str(e)}"

def import_from_csv(filename):
    """استيراد البيانات من ملف CSV"""
    try:
        data = []
        with open(filename, 'r', encoding='utf-8-sig') as csvfile:
            reader = csv.reader(csvfile)
            for row in reader:
                data.append(row)
        
        return True, data
    except Exception as e:
        return False, f"خطأ في استيراد البيانات: {str(e)}"

def save_json_file(data, filename):
    """حفظ البيانات في ملف JSON"""
    try:
        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(data, jsonfile, ensure_ascii=False, indent=4)
        
        return True, f"تم حفظ البيانات في {filename}"
    except Exception as e:
        return False, f"خطأ في حفظ البيانات: {str(e)}"

def load_json_file(filename):
    """تحميل البيانات من ملف JSON"""
    try:
        with open(filename, 'r', encoding='utf-8') as jsonfile:
            data = json.load(jsonfile)
        
        return True, data
    except Exception as e:
        return False, f"خطأ في تحميل البيانات: {str(e)}"

def select_file_dialog(title="اختر ملف", filetypes=None):
    """حوار اختيار ملف"""
    if filetypes is None:
        filetypes = [("جميع الملفات", "*.*")]
    
    filename = filedialog.askopenfilename(
        title=title,
        filetypes=filetypes
    )
    
    return filename

def select_save_file_dialog(title="حفظ باسم", defaultextension=".txt", filetypes=None):
    """حوار حفظ ملف"""
    if filetypes is None:
        filetypes = [("ملفات نصية", "*.txt"), ("جميع الملفات", "*.*")]
    
    filename = filedialog.asksaveasfilename(
        title=title,
        defaultextension=defaultextension,
        filetypes=filetypes
    )
    
    return filename

def select_directory_dialog(title="اختر مجلد"):
    """حوار اختيار مجلد"""
    directory = filedialog.askdirectory(title=title)
    return directory

def calculate_age(birth_date_str):
    """حساب العمر من تاريخ الميلاد"""
    try:
        if '/' in birth_date_str:
            # تنسيق DD/MM/YYYY
            day, month, year = birth_date_str.split('/')
        elif '-' in birth_date_str:
            # تنسيق YYYY-MM-DD
            year, month, day = birth_date_str.split('-')
        else:
            return None
        
        birth_date = datetime(int(year), int(month), int(day))
        today = datetime.now()
        
        age = today.year - birth_date.year
        
        # تعديل العمر إذا لم يحن عيد الميلاد بعد
        if today.month < birth_date.month or (today.month == birth_date.month and today.day < birth_date.day):
            age -= 1
        
        return age
    except:
        return None

def format_datetime(dt, format_type="full"):
    """تنسيق التاريخ والوقت"""
    if isinstance(dt, str):
        try:
            dt = datetime.fromisoformat(dt.replace('Z', '+00:00'))
        except:
            return dt
    
    if format_type == "date_only":
        return dt.strftime("%Y-%m-%d")
    elif format_type == "time_only":
        return dt.strftime("%H:%M:%S")
    elif format_type == "short":
        return dt.strftime("%Y-%m-%d %H:%M")
    else:  # full
        return dt.strftime("%Y-%m-%d %H:%M:%S")

def clean_text(text):
    """تنظيف النص من المسافات الزائدة والأحرف غير المرغوبة"""
    if not text:
        return ""
    
    # إزالة المسافات من البداية والنهاية
    text = text.strip()
    
    # استبدال المسافات المتعددة بمسافة واحدة
    text = ' '.join(text.split())
    
    return text

def truncate_text(text, max_length=50, suffix="..."):
    """اقتطاع النص إذا كان طويلاً"""
    if not text:
        return ""
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix

def generate_report_filename(report_type, section=None, date=None):
    """توليد اسم ملف التقرير"""
    if date is None:
        date = datetime.now()
    
    date_str = date.strftime("%Y%m%d")
    
    if section:
        return f"{report_type}_{section}_{date_str}.pdf"
    else:
        return f"{report_type}_{date_str}.pdf"

def validate_file_extension(filename, allowed_extensions):
    """التحقق من امتداد الملف"""
    if not filename:
        return False
    
    file_ext = os.path.splitext(filename)[1].lower()
    return file_ext in [ext.lower() for ext in allowed_extensions]

def get_file_info(filepath):
    """الحصول على معلومات الملف"""
    try:
        if not os.path.exists(filepath):
            return None
        
        stat = os.stat(filepath)
        
        return {
            'name': os.path.basename(filepath),
            'size': stat.st_size,
            'size_formatted': format_file_size(stat.st_size),
            'created': datetime.fromtimestamp(stat.st_ctime),
            'modified': datetime.fromtimestamp(stat.st_mtime),
            'extension': os.path.splitext(filepath)[1]
        }
    except Exception as e:
        print(f"خطأ في الحصول على معلومات الملف: {e}")
        return None

def center_window(window, width=None, height=None):
    """توسيط النافذة على الشاشة"""
    window.update_idletasks()
    
    if width is None:
        width = window.winfo_width()
    if height is None:
        height = window.winfo_height()
    
    screen_width = window.winfo_screenwidth()
    screen_height = window.winfo_screenheight()
    
    x = (screen_width // 2) - (width // 2)
    y = (screen_height // 2) - (height // 2)
    
    window.geometry(f'{width}x{height}+{x}+{y}')

def create_tooltip(widget, text):
    """إنشاء تلميح للعنصر"""
    def on_enter(event):
        tooltip = tk.Toplevel()
        tooltip.wm_overrideredirect(True)
        tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
        
        label = tk.Label(
            tooltip,
            text=text,
            background="lightyellow",
            relief="solid",
            borderwidth=1,
            font=("Arial", 9)
        )
        label.pack()
        
        widget.tooltip = tooltip
    
    def on_leave(event):
        if hasattr(widget, 'tooltip'):
            widget.tooltip.destroy()
            del widget.tooltip
    
    widget.bind("<Enter>", on_enter)
    widget.bind("<Leave>", on_leave)

def setup_window_icon(window, icon_path=None):
    """إعداد أيقونة النافذة"""
    try:
        if icon_path and os.path.exists(icon_path):
            window.iconbitmap(icon_path)
        else:
            # محاولة العثور على أيقونة افتراضية
            default_icon = os.path.join(
                os.path.dirname(os.path.dirname(__file__)),
                "resources", "icons", "app_icon.ico"
            )
            if os.path.exists(default_icon):
                window.iconbitmap(default_icon)
    except Exception as e:
        print(f"تعذر تحميل أيقونة النافذة: {e}")

class ProgressDialog:
    """حوار شريط التقدم"""
    
    def __init__(self, parent, title="جاري المعالجة..."):
        self.parent = parent
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("300x100")
        self.dialog.resizable(False, False)
        
        # توسيط الحوار
        center_window(self.dialog, 300, 100)
        
        # منع إغلاق الحوار
        self.dialog.protocol("WM_DELETE_WINDOW", lambda: None)
        
        # تسمية الحالة
        self.status_label = tk.Label(self.dialog, text="جاري المعالجة...")
        self.status_label.pack(pady=10)
        
        # شريط التقدم
        from tkinter import ttk
        self.progress = ttk.Progressbar(
            self.dialog,
            mode='indeterminate',
            length=250
        )
        self.progress.pack(pady=10)
        self.progress.start()
        
        # جعل الحوار في المقدمة
        self.dialog.transient(parent)
        self.dialog.grab_set()
    
    def update_status(self, status):
        """تحديث نص الحالة"""
        self.status_label.config(text=status)
        self.dialog.update()
    
    def close(self):
        """إغلاق الحوار"""
        self.progress.stop()
        self.dialog.grab_release()
        self.dialog.destroy()
