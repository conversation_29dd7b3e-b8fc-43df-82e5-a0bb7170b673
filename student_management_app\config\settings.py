#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات التطبيق
Application Settings

معهد المتوسط للمهن الشاملة - اجخرة
"""

import json
import os
from datetime import datetime

class AppSettings:
    """فئة إدارة إعدادات التطبيق"""
    
    def __init__(self):
        """تهيئة الإعدادات"""
        self.settings_file = os.path.join(
            os.path.dirname(os.path.dirname(__file__)), 
            "data", 
            "app_settings.json"
        )
        
        # الإعدادات الافتراضية
        self.default_settings = {
            "institute_info": {
                "name": "معهد المتوسط للمهن الشاملة",
                "location": "اجخرة",
                "phone": "",
                "email": "",
                "logo_path": ""
            },
            "academic_year": {
                "current_year": "2024-2025",
                "semester": "الفصل الأول"
            },
            "ui_settings": {
                "theme": "default",
                "font_size": 12,
                "window_size": {
                    "width": 1400,
                    "height": 900
                },
                "language": "arabic"
            },
            "database_settings": {
                "backup_interval": 7,  # أيام
                "auto_backup": True,
                "backup_location": "backups"
            },
            "printing_settings": {
                "default_printer": "",
                "paper_size": "A4",
                "margins": {
                    "top": 20,
                    "bottom": 20,
                    "left": 20,
                    "right": 20
                }
            },
            "grade_settings": {
                "passing_grade": 50,
                "max_grade": 100,
                "grade_scale": {
                    "excellent": 90,
                    "very_good": 80,
                    "good": 70,
                    "acceptable": 60,
                    "weak": 50
                }
            },
            "system_settings": {
                "auto_save": True,
                "save_interval": 5,  # دقائق
                "show_tips": True,
                "check_updates": True
            }
        }
        
        # تحميل الإعدادات
        self.settings = self.load_settings()
    
    def load_settings(self):
        """تحميل الإعدادات من الملف"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                
                # دمج الإعدادات المحملة مع الافتراضية
                settings = self.default_settings.copy()
                self._merge_settings(settings, loaded_settings)
                return settings
            else:
                # إنشاء ملف الإعدادات الافتراضي
                self.save_settings(self.default_settings)
                return self.default_settings.copy()
                
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
            return self.default_settings.copy()
    
    def _merge_settings(self, default, loaded):
        """دمج الإعدادات المحملة مع الافتراضية"""
        for key, value in loaded.items():
            if key in default:
                if isinstance(value, dict) and isinstance(default[key], dict):
                    self._merge_settings(default[key], value)
                else:
                    default[key] = value
    
    def save_settings(self, settings=None):
        """حفظ الإعدادات في الملف"""
        try:
            if settings is None:
                settings = self.settings
            
            # إنشاء مجلد البيانات إذا لم يكن موجوداً
            os.makedirs(os.path.dirname(self.settings_file), exist_ok=True)
            
            # إضافة معلومات الحفظ
            settings['last_saved'] = datetime.now().isoformat()
            
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=4)
            
            return True
            
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")
            return False
    
    def get(self, key_path, default=None):
        """الحصول على قيمة إعداد باستخدام مسار النقاط"""
        try:
            keys = key_path.split('.')
            value = self.settings
            
            for key in keys:
                value = value[key]
            
            return value
            
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path, value):
        """تعيين قيمة إعداد باستخدام مسار النقاط"""
        try:
            keys = key_path.split('.')
            settings = self.settings
            
            # الانتقال إلى المستوى الأخير
            for key in keys[:-1]:
                if key not in settings:
                    settings[key] = {}
                settings = settings[key]
            
            # تعيين القيمة
            settings[keys[-1]] = value
            return True
            
        except Exception as e:
            print(f"خطأ في تعيين الإعداد {key_path}: {e}")
            return False
    
    def reset_to_default(self):
        """إعادة تعيين الإعدادات إلى القيم الافتراضية"""
        self.settings = self.default_settings.copy()
        return self.save_settings()
    
    def export_settings(self, file_path):
        """تصدير الإعدادات إلى ملف"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"خطأ في تصدير الإعدادات: {e}")
            return False
    
    def import_settings(self, file_path):
        """استيراد الإعدادات من ملف"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_settings = json.load(f)
            
            # دمج الإعدادات المستوردة
            self._merge_settings(self.settings, imported_settings)
            return self.save_settings()
            
        except Exception as e:
            print(f"خطأ في استيراد الإعدادات: {e}")
            return False
    
    # خصائص سريعة للوصول للإعدادات الشائعة
    @property
    def institute_name(self):
        return self.get('institute_info.name', 'معهد المتوسط للمهن الشاملة')
    
    @property
    def institute_location(self):
        return self.get('institute_info.location', 'اجخرة')
    
    @property
    def current_academic_year(self):
        return self.get('academic_year.current_year', '2024-2025')
    
    @property
    def current_semester(self):
        return self.get('academic_year.semester', 'الفصل الأول')
    
    @property
    def font_size(self):
        return self.get('ui_settings.font_size', 12)
    
    @property
    def passing_grade(self):
        return self.get('grade_settings.passing_grade', 50)
    
    @property
    def max_grade(self):
        return self.get('grade_settings.max_grade', 100)
    
    def get_window_size(self):
        """الحصول على حجم النافذة"""
        width = self.get('ui_settings.window_size.width', 1400)
        height = self.get('ui_settings.window_size.height', 900)
        return width, height
    
    def set_window_size(self, width, height):
        """تعيين حجم النافذة"""
        self.set('ui_settings.window_size.width', width)
        self.set('ui_settings.window_size.height', height)
    
    def get_grade_description(self, grade):
        """الحصول على وصف الدرجة"""
        grade = float(grade)
        
        if grade >= self.get('grade_settings.grade_scale.excellent', 90):
            return 'ممتاز'
        elif grade >= self.get('grade_settings.grade_scale.very_good', 80):
            return 'جيد جداً'
        elif grade >= self.get('grade_settings.grade_scale.good', 70):
            return 'جيد'
        elif grade >= self.get('grade_settings.grade_scale.acceptable', 60):
            return 'مقبول'
        elif grade >= self.get('grade_settings.passing_grade', 50):
            return 'ضعيف'
        else:
            return 'راسب'
