# متطلبات نظام إدارة الطلاب المحسن
# Enhanced Student Management System Requirements

# المكتبات الأساسية (مثبتة افتراضياً مع Python 3.6+)
# Core libraries (included with Python 3.6+ by default)

# tkinter - واجهة المستخدم الرسومية
# GUI framework (included with Python)

# sqlite3 - قاعدة البيانات
# Database engine (included with Python)

# datetime - التعامل مع التواريخ والأوقات
# Date and time handling (included with Python)

# json - التعامل مع ملفات JSON
# JSON file handling (included with Python)

# os - عمليات نظام التشغيل
# Operating system interface (included with Python)

# sys - معلومات النظام
# System-specific parameters (included with Python)

# platform - معلومات المنصة
# Platform identification (included with Python)

# re - التعبيرات النمطية
# Regular expressions (included with Python)

# csv - التعامل مع ملفات CSV
# CSV file handling (included with Python)

# shutil - عمليات الملفات المتقدمة
# High-level file operations (included with Python)

# threading - البرمجة متعددة الخيوط
# Threading support (included with Python)

# hashlib - دوال التشفير
# Cryptographic hash functions (included with Python)

# ملاحظات:
# Notes:

# 1. جميع المكتبات المطلوبة مثبتة افتراضياً مع Python 3.6+
#    All required libraries are included with Python 3.6+ by default

# 2. لا حاجة لتثبيت مكتبات إضافية
#    No additional libraries need to be installed

# 3. للتشغيل على Windows:
#    To run on Windows:
#    - انقر نقراً مزدوجاً على run_app.bat
#    - Double-click run_app.bat

# 4. للتشغيل من سطر الأوامر:
#    To run from command line:
#    python main.py

# 5. متطلبات النظام:
#    System requirements:
#    - Python 3.6 أو أحدث / Python 3.6 or newer
#    - Windows 7/8/10/11, macOS 10.12+, أو Linux
#    - 100 MB مساحة فارغة على القرص الصلب
#    - 512 MB ذاكرة وصول عشوائي (RAM)

# 6. للحصول على الدعم:
#    For support:
#    تواصل مع إدارة النظام في المعهد
#    Contact the system administration at the institute

# إصدار Python المطلوب:
# Required Python version:
# python_requires>=3.6
