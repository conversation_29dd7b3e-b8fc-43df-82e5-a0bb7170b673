#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إعدادات النظام
System Settings Window
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sys
import os
import json

# إضافة مسار المكتبات
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.arabic_support import *

class SettingsWindow:
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = tk.Toplevel(parent)
        self.settings_file = "settings.json"
        self.settings = self.load_settings()
        self.setup_window()
        self.setup_ui()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إعدادات النظام")
        self.window.geometry("700x600")
        self.window.configure(bg='#ecf0f1')
        self.window.resizable(True, True)
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # توسيط النافذة
        self.center_window()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def load_settings(self):
        """تحميل الإعدادات من الملف"""
        default_settings = {
            "institute_name": "معهد المتوسط للمهن الشاملة - اجخرة",
            "institute_address": "اجخرة - ليبيا",
            "director_name": "مدير المعهد",
            "phone": "",
            "email": "",
            "backup_path": "",
            "auto_backup": False,
            "theme": "default"
        }
        
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                    default_settings.update(loaded_settings)
        except Exception:
            pass
        
        return default_settings
    
    def save_settings(self):
        """حفظ الإعدادات في الملف"""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الإعدادات: {str(e)}")
            return False
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار العنوان
        title_frame = tk.Frame(self.window, bg='#2c3e50', height=60)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame,
            text="إعدادات النظام",
            font=get_arabic_font(size=18, weight='bold'),
            fg='white',
            bg='#2c3e50'
        )
        title_label.pack(pady=15)
        
        # دفتر التبويبات
        notebook = ttk.Notebook(self.window)
        notebook.pack(fill='both', expand=True, padx=20, pady=20)
        
        # تبويب المعلومات العامة
        self.create_general_tab(notebook)
        
        # تبويب النسخ الاحتياطي
        self.create_backup_tab(notebook)
        
        # تبويب إدارة البيانات
        self.create_data_tab(notebook)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.window, bg='#ecf0f1')
        buttons_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        save_btn = create_arabic_button(
            buttons_frame,
            "حفظ الإعدادات",
            command=self.save_all_settings,
            bg='#27ae60',
            fg='white',
            width=15
        )
        save_btn.pack(side='right', padx=(0, 10))
        
        cancel_btn = create_arabic_button(
            buttons_frame,
            "إلغاء",
            command=self.window.destroy,
            bg='#e74c3c',
            fg='white',
            width=10
        )
        cancel_btn.pack(side='left')
    
    def create_general_tab(self, notebook):
        """إنشاء تبويب المعلومات العامة"""
        general_frame = tk.Frame(notebook, bg='#ecf0f1')
        notebook.add(general_frame, text="المعلومات العامة")
        
        # إطار المحتوى
        content_frame = tk.Frame(general_frame, bg='#ecf0f1')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # اسم المعهد
        tk.Label(
            content_frame,
            text="اسم المعهد:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1',
            fg='#2c3e50'
        ).grid(row=0, column=1, sticky='e', padx=(0, 10), pady=10)
        
        self.institute_name_var = tk.StringVar(value=self.settings['institute_name'])
        institute_entry = RTLEntry(
            content_frame,
            textvariable=self.institute_name_var,
            font=get_arabic_font(size=12),
            width=40
        )
        institute_entry.grid(row=0, column=0, sticky='ew', pady=10)
        
        # عنوان المعهد
        tk.Label(
            content_frame,
            text="عنوان المعهد:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1',
            fg='#2c3e50'
        ).grid(row=1, column=1, sticky='e', padx=(0, 10), pady=10)
        
        self.institute_address_var = tk.StringVar(value=self.settings['institute_address'])
        address_entry = RTLEntry(
            content_frame,
            textvariable=self.institute_address_var,
            font=get_arabic_font(size=12),
            width=40
        )
        address_entry.grid(row=1, column=0, sticky='ew', pady=10)
        
        # اسم المدير
        tk.Label(
            content_frame,
            text="اسم المدير:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1',
            fg='#2c3e50'
        ).grid(row=2, column=1, sticky='e', padx=(0, 10), pady=10)
        
        self.director_name_var = tk.StringVar(value=self.settings['director_name'])
        director_entry = RTLEntry(
            content_frame,
            textvariable=self.director_name_var,
            font=get_arabic_font(size=12),
            width=40
        )
        director_entry.grid(row=2, column=0, sticky='ew', pady=10)
        
        # رقم الهاتف
        tk.Label(
            content_frame,
            text="رقم الهاتف:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1',
            fg='#2c3e50'
        ).grid(row=3, column=1, sticky='e', padx=(0, 10), pady=10)
        
        self.phone_var = tk.StringVar(value=self.settings['phone'])
        phone_entry = tk.Entry(
            content_frame,
            textvariable=self.phone_var,
            font=get_arabic_font(size=12),
            width=40
        )
        phone_entry.grid(row=3, column=0, sticky='ew', pady=10)
        
        # البريد الإلكتروني
        tk.Label(
            content_frame,
            text="البريد الإلكتروني:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1',
            fg='#2c3e50'
        ).grid(row=4, column=1, sticky='e', padx=(0, 10), pady=10)
        
        self.email_var = tk.StringVar(value=self.settings['email'])
        email_entry = tk.Entry(
            content_frame,
            textvariable=self.email_var,
            font=get_arabic_font(size=12),
            width=40
        )
        email_entry.grid(row=4, column=0, sticky='ew', pady=10)
        
        # تكوين الشبكة
        content_frame.grid_columnconfigure(0, weight=1)
    
    def create_backup_tab(self, notebook):
        """إنشاء تبويب النسخ الاحتياطي"""
        backup_frame = tk.Frame(notebook, bg='#ecf0f1')
        notebook.add(backup_frame, text="النسخ الاحتياطي")
        
        content_frame = tk.Frame(backup_frame, bg='#ecf0f1')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # مسار النسخ الاحتياطي
        path_frame = tk.Frame(content_frame, bg='#ecf0f1')
        path_frame.pack(fill='x', pady=10)
        
        tk.Label(
            path_frame,
            text="مسار النسخ الاحتياطي:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1',
            fg='#2c3e50'
        ).pack(anchor='e', pady=(0, 5))
        
        path_input_frame = tk.Frame(path_frame, bg='#ecf0f1')
        path_input_frame.pack(fill='x')
        
        self.backup_path_var = tk.StringVar(value=self.settings['backup_path'])
        path_entry = tk.Entry(
            path_input_frame,
            textvariable=self.backup_path_var,
            font=get_arabic_font(size=12)
        )
        path_entry.pack(side='right', fill='x', expand=True, padx=(0, 10))
        
        browse_btn = create_arabic_button(
            path_input_frame,
            "تصفح",
            command=self.browse_backup_path,
            bg='#3498db',
            fg='white',
            width=10
        )
        browse_btn.pack(side='right')
        
        # النسخ الاحتياطي التلقائي
        self.auto_backup_var = tk.BooleanVar(value=self.settings['auto_backup'])
        auto_backup_check = tk.Checkbutton(
            content_frame,
            text="تفعيل النسخ الاحتياطي التلقائي",
            variable=self.auto_backup_var,
            font=get_arabic_font(size=12),
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        auto_backup_check.pack(anchor='e', pady=20)
        
        # أزرار النسخ الاحتياطي
        backup_buttons_frame = tk.Frame(content_frame, bg='#ecf0f1')
        backup_buttons_frame.pack(fill='x', pady=20)
        
        create_backup_btn = create_arabic_button(
            backup_buttons_frame,
            "إنشاء نسخة احتياطية",
            command=self.create_backup,
            bg='#27ae60',
            fg='white',
            width=20
        )
        create_backup_btn.pack(side='right', padx=(0, 10))
        
        restore_backup_btn = create_arabic_button(
            backup_buttons_frame,
            "استعادة نسخة احتياطية",
            command=self.restore_backup,
            bg='#f39c12',
            fg='white',
            width=20
        )
        restore_backup_btn.pack(side='right')
    
    def create_data_tab(self, notebook):
        """إنشاء تبويب إدارة البيانات"""
        data_frame = tk.Frame(notebook, bg='#ecf0f1')
        notebook.add(data_frame, text="إدارة البيانات")
        
        content_frame = tk.Frame(data_frame, bg='#ecf0f1')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # إدارة الشعب
        sections_frame = tk.LabelFrame(
            content_frame,
            text="إدارة الشعب",
            font=get_arabic_font(size=14, weight='bold'),
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        sections_frame.pack(fill='x', pady=(0, 20))
        
        sections_content = tk.Frame(sections_frame, bg='#ecf0f1')
        sections_content.pack(fill='x', padx=20, pady=15)
        
        add_section_btn = create_arabic_button(
            sections_content,
            "إضافة شعبة جديدة",
            command=self.add_new_section,
            bg='#3498db',
            fg='white',
            width=20
        )
        add_section_btn.pack(side='right')
        
        # تنظيف البيانات
        cleanup_frame = tk.LabelFrame(
            content_frame,
            text="تنظيف البيانات",
            font=get_arabic_font(size=14, weight='bold'),
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        cleanup_frame.pack(fill='x', pady=(0, 20))
        
        cleanup_content = tk.Frame(cleanup_frame, bg='#ecf0f1')
        cleanup_content.pack(fill='x', padx=20, pady=15)
        
        tk.Label(
            cleanup_content,
            text="تحذير: هذه العمليات لا يمكن التراجع عنها!",
            font=get_arabic_font(size=12, weight='bold'),
            bg='#ecf0f1',
            fg='#e74c3c'
        ).pack(anchor='e', pady=(0, 10))
        
        delete_grades_btn = create_arabic_button(
            cleanup_content,
            "حذف جميع الدرجات",
            command=self.delete_all_grades,
            bg='#e74c3c',
            fg='white',
            width=20
        )
        delete_grades_btn.pack(side='right', padx=(0, 10))
        
        delete_students_btn = create_arabic_button(
            cleanup_content,
            "حذف جميع الطلاب",
            command=self.delete_all_students,
            bg='#e74c3c',
            fg='white',
            width=20
        )
        delete_students_btn.pack(side='right')
    
    def browse_backup_path(self):
        """تصفح مسار النسخ الاحتياطي"""
        path = filedialog.askdirectory(title="اختيار مجلد النسخ الاحتياطي")
        if path:
            self.backup_path_var.set(path)
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        backup_path = self.backup_path_var.get()
        if not backup_path:
            messagebox.showwarning("تحذير", "يرجى تحديد مسار النسخ الاحتياطي")
            return
        
        try:
            import shutil
            from datetime import datetime
            
            # إنشاء اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"student_backup_{timestamp}.db"
            backup_filepath = os.path.join(backup_path, backup_filename)
            
            # نسخ قاعدة البيانات
            shutil.copy2(self.db_manager.db_path, backup_filepath)
            
            messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية:\n{backup_filepath}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
    
    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        backup_file = filedialog.askopenfilename(
            title="اختيار ملف النسخة الاحتياطية",
            filetypes=[("Database files", "*.db"), ("All files", "*.*")]
        )
        
        if not backup_file:
            return
        
        result = messagebox.askyesno(
            "تأكيد الاستعادة",
            "هل أنت متأكد من استعادة النسخة الاحتياطية؟\nسيتم استبدال البيانات الحالية!"
        )
        
        if result:
            try:
                import shutil
                shutil.copy2(backup_file, self.db_manager.db_path)
                messagebox.showinfo("نجح", "تم استعادة النسخة الاحتياطية بنجاح")
                
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في استعادة النسخة الاحتياطية: {str(e)}")
    
    def add_new_section(self):
        """إضافة شعبة جديدة"""
        dialog = tk.Toplevel(self.window)
        dialog.title("إضافة شعبة جديدة")
        dialog.geometry("400x200")
        dialog.configure(bg='#ecf0f1')
        dialog.transient(self.window)
        dialog.grab_set()
        
        # توسيط النافذة
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (200)
        y = (dialog.winfo_screenheight() // 2) - (100)
        dialog.geometry(f'400x200+{x}+{y}')
        
        tk.Label(
            dialog,
            text="اسم الشعبة الجديدة:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1',
            fg='#2c3e50'
        ).pack(pady=20)
        
        section_var = tk.StringVar()
        section_entry = RTLEntry(
            dialog,
            textvariable=section_var,
            font=get_arabic_font(size=12),
            width=30
        )
        section_entry.pack(pady=10)
        section_entry.focus()
        
        buttons_frame = tk.Frame(dialog, bg='#ecf0f1')
        buttons_frame.pack(pady=20)
        
        def save_section():
            section_name = section_var.get().strip()
            if not section_name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم الشعبة")
                return
            
            try:
                self.db_manager.add_section(section_name)
                messagebox.showinfo("نجح", "تم إضافة الشعبة بنجاح")
                dialog.destroy()
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في إضافة الشعبة: {str(e)}")
        
        save_btn = create_arabic_button(
            buttons_frame,
            "حفظ",
            command=save_section,
            bg='#27ae60',
            fg='white',
            width=10
        )
        save_btn.pack(side='right', padx=(0, 10))
        
        cancel_btn = create_arabic_button(
            buttons_frame,
            "إلغاء",
            command=dialog.destroy,
            bg='#e74c3c',
            fg='white',
            width=10
        )
        cancel_btn.pack(side='right')
    
    def delete_all_grades(self):
        """حذف جميع الدرجات"""
        result = messagebox.askyesno(
            "تأكيد الحذف",
            "هل أنت متأكد من حذف جميع الدرجات؟\nهذه العملية لا يمكن التراجع عنها!"
        )
        
        if result:
            try:
                import sqlite3
                conn = sqlite3.connect(self.db_manager.db_path)
                cursor = conn.cursor()
                cursor.execute("DELETE FROM grades")
                conn.commit()
                conn.close()
                messagebox.showinfo("تم الحذف", "تم حذف جميع الدرجات بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حذف الدرجات: {str(e)}")
    
    def delete_all_students(self):
        """حذف جميع الطلاب"""
        result = messagebox.askyesno(
            "تأكيد الحذف",
            "هل أنت متأكد من حذف جميع الطلاب؟\nسيتم حذف الدرجات أيضاً!\nهذه العملية لا يمكن التراجع عنها!"
        )
        
        if result:
            try:
                import sqlite3
                conn = sqlite3.connect(self.db_manager.db_path)
                cursor = conn.cursor()
                cursor.execute("DELETE FROM grades")
                cursor.execute("DELETE FROM students")
                conn.commit()
                conn.close()
                messagebox.showinfo("تم الحذف", "تم حذف جميع الطلاب والدرجات بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حذف الطلاب: {str(e)}")
    
    def save_all_settings(self):
        """حفظ جميع الإعدادات"""
        self.settings['institute_name'] = self.institute_name_var.get()
        self.settings['institute_address'] = self.institute_address_var.get()
        self.settings['director_name'] = self.director_name_var.get()
        self.settings['phone'] = self.phone_var.get()
        self.settings['email'] = self.email_var.get()
        self.settings['backup_path'] = self.backup_path_var.get()
        self.settings['auto_backup'] = self.auto_backup_var.get()
        
        if self.save_settings():
            messagebox.showinfo("تم الحفظ", "تم حفظ الإعدادات بنجاح")
            self.window.destroy()

# نقطة تشغيل مستقلة لاختبار النافذة
if __name__ == "__main__":
    class DummyDB:
        def __init__(self):
            self.db_path = "test.db"
        
        def add_section(self, name):
            print(f"إضافة شعبة: {name}")

    root = tk.Tk()
    root.withdraw()
    app = SettingsWindow(root, DummyDB())
    root.mainloop()
