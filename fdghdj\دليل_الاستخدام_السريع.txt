دليل الاستخدام السريع - نظام إدارة الطلاب
==========================================

🚀 كيفية تشغيل البرنامج:
========================

الطريقة الأسهل:
انقر نقراً مزدوجاً على: تشغيل_نظام_الطلاب.bat

أو من سطر الأوامر:
python run_student_system.py

🔧 إذا لم يعمل البرنامج:
======================

1. شغل أداة التشخيص:
   python تشخيص_المشاكل.py

2. شغل الإصلاح السريع:
   python إصلاح_سريع.py

3. أعد المحاولة:
   python run_student_system.py

🎯 ميزة جديدة: نظام اختيار الطلاب المركزي
==========================================

الآن يمكنك:
✅ اختيار طالب مرة واحدة واستخدامه في جميع العمليات
✅ تحرير بيانات الطالب مباشرة من النافذة الرئيسية
✅ إنتاج الشهادات للطالب المحدد تلقائياً
✅ عرض الدرجات والتقارير بنقرة واحدة

كيفية الاستخدام:
1. اضغط "اختيار طالب" في النافذة الرئيسية
2. ابحث واختر الطالب المطلوب
3. استخدم أزرار "تحرير البيانات" والعمليات الأخرى
4. الطالب المحدد يبقى نشطاً في جميع النوافذ

للتفاصيل الكاملة، راجع ملف: STUDENT_SELECTION_GUIDE.md

📋 الاستخدام الأساسي:
====================

الخطوة 1: إعداد النظام
-----------------------
1. شغل البرنامج
2. اضغط "إعدادات النظام"
3. أدخل معلومات المعهد في تبويب "المعلومات العامة"
4. احفظ الإعدادات

الخطوة 2: إضافة الشعب
---------------------
1. في نافذة الإعدادات
2. اذهب إلى تبويب "إدارة البيانات"
3. اضغط "إضافة شعبة جديدة"
4. أدخل اسم الشعبة واحفظ

الخطوة 3: إدخال بيانات الطلاب
-----------------------------
1. من النافذة الرئيسية اضغط "إدخال بيانات الطلاب"
2. املأ البيانات:
   - الاسم الكامل
   - الرقم الوطني
   - تاريخ الميلاد
   - الجنس
   - رقم الهاتف
   - الشعبة
3. اضغط "حفظ"
4. سيتم توليد رقم الجلوس تلقائياً

الخطوة 4: إدخال الدرجات
------------------------
1. اضغط "إدخال الدرجات"
2. اختر الشعبة والمادة
3. اضغط "تحميل الطلاب"
4. أدخل الدرجات لكل طالب:
   - عمل الفصل الأول (من 20)
   - امتحان الفصل الأول (من 30)
   - عمل الفصل الثاني (من 20)
   - امتحان الفصل الثاني (من 30)
5. اضغط "حفظ الدرجات"

الخطوة 5: البحث عن الطلاب
--------------------------
1. اضغط "البحث عن طالب"
2. أدخل الاسم أو الرقم الوطني أو رقم الجلوس
3. اضغط "بحث"
4. انقر نقراً مزدوجاً على الطالب لعرض درجاته

الخطوة 6: إنتاج الشهادات
-------------------------
1. اضغط "إنتاج الشهادات"
2. اختر الشعبة والسنة الدراسية
3. اختر نوع الشهادة (نجاح أو درجات)
4. حدد الطلاب المطلوبين (انقر على مربع الاختيار)
5. اضغط "معاينة" لرؤية الشهادة
6. اضغط "إنتاج الشهادات" واختر مجلد الحفظ

الخطوة 7: بطاقات أرقام الجلوس
------------------------------
1. اضغط "بطاقات أرقام الجلوس"
2. اختر الشعبة
3. أدخل معلومات الامتحان
4. حدد الطلاب المطلوبين
5. اضغط "إنتاج البطاقات"

💾 النسخ الاحتياطي:
==================

إنشاء نسخة احتياطية:
1. اذهب إلى "إعدادات النظام"
2. تبويب "النسخ الاحتياطي"
3. حدد مجلد الحفظ
4. اضغط "إنشاء نسخة احتياطية"

استعادة نسخة احتياطية:
1. في نفس التبويب
2. اضغط "استعادة نسخة احتياطية"
3. اختر ملف النسخة الاحتياطية (.db)

🎯 نصائح مهمة:
==============

✅ احفظ نسخة احتياطية دورياً
✅ تأكد من صحة البيانات قبل الحفظ
✅ استخدم أرقام وطنية صحيحة (لا تكرار)
✅ راجع الدرجات قبل إنتاج الشهادات
✅ احتفظ بنسخة من الشهادات المطبوعة

⚠️ تحذيرات:
============

❌ لا تحذف ملفات البرنامج
❌ لا تعدل في قاعدة البيانات مباشرة
❌ تأكد من النسخ الاحتياطي قبل حذف البيانات
❌ لا تشغل أكثر من نسخة من البرنامج

🆘 للمساعدة:
=============

إذا واجهت مشاكل:
1. راجع ملف "تعليمات_التشغيل.txt"
2. شغل أداة التشخيص
3. راجع ملف README.md
4. تأكد من تحديث Python

الملفات المهمة:
- تشغيل_نظام_الطلاب.bat (للتشغيل)
- تشخيص_المشاكل.py (لحل المشاكل)
- إصلاح_سريع.py (للإصلاح)
- data/student_data.db (قاعدة البيانات)

نصيحة: احتفظ بنسخة من مجلد البرنامج كاملاً كنسخة احتياطية!
