#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدخال الدرجات
Grade Entry Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import sys
import os

# إضافة مسار المكتبات
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.arabic_support import *
from utils.student_selector import student_selector

class GradeEntryWindow:
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.window = tk.Toplevel(parent)
        self.current_students = []
        self.current_subjects = []
        self.setup_window()
        self.setup_ui()

        # التحقق من وجود طالب محدد
        self.check_selected_student()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدخال الدرجات")
        self.window.geometry("1000x700")
        self.window.configure(bg='#ecf0f1')
        self.window.resizable(True, True)
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def check_selected_student(self):
        """التحقق من وجود طالب محدد وتحميل شعبته"""
        selected_student = student_selector.get_selected_student()
        if selected_student:
            # تحديد الشعبة تلقائياً
            if hasattr(self, 'section_var'):
                self.section_var.set(selected_student['section'])
                self.on_section_changed()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_frame = tk.Frame(self.window, bg='#e74c3c', height=60)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame,
            text="إدخال الدرجات",
            font=get_arabic_font(size=18, weight='bold'),
            bg='#e74c3c',
            fg='white'
        )
        title_label.pack(pady=15)
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.window, bg='#ecf0f1')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # إنشاء لوحة التحكم
        self.create_control_panel(main_frame)
        
        # إنشاء جدول الدرجات
        self.create_grades_table(main_frame)
        
    def create_control_panel(self, parent):
        """إنشاء لوحة التحكم"""
        control_frame = tk.LabelFrame(
            parent,
            text="اختيار الشعبة والمادة",
            font=get_arabic_font(size=14, weight='bold'),
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        control_frame.pack(fill='x', pady=(0, 20))
        
        # الصف الأول - اختيار الشعبة والمادة
        row1_frame = tk.Frame(control_frame, bg='#ecf0f1')
        row1_frame.pack(fill='x', padx=10, pady=10)
        
        # اختيار الشعبة
        tk.Label(
            row1_frame,
            text="الشعبة:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1',
            anchor='e'
        ).pack(side='right', padx=(0, 10))
        
        self.section_var = tk.StringVar()
        self.section_combo = ttk.Combobox(
            row1_frame,
            textvariable=self.section_var,
            font=get_arabic_font(size=12),
            width=20,
            state='readonly'
        )
        self.section_combo.pack(side='right', padx=(0, 20))
        self.section_combo.bind('<<ComboboxSelected>>', self.on_section_changed)
        
        # اختيار المادة
        tk.Label(
            row1_frame,
            text="المادة:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1',
            anchor='e'
        ).pack(side='right', padx=(0, 10))
        
        self.subject_var = tk.StringVar()
        self.subject_combo = ttk.Combobox(
            row1_frame,
            textvariable=self.subject_var,
            font=get_arabic_font(size=12),
            width=20,
            state='readonly'
        )
        self.subject_combo.pack(side='right', padx=(0, 20))
        self.subject_combo.bind('<<ComboboxSelected>>', self.on_subject_changed)
        
        # السنة الدراسية
        tk.Label(
            row1_frame,
            text="السنة الدراسية:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1',
            anchor='e'
        ).pack(side='right', padx=(0, 10))
        
        self.academic_year_var = tk.StringVar()
        current_year = datetime.now().year
        self.academic_year_var.set(f"{current_year}-{current_year + 1}")
        
        self.academic_year_entry = tk.Entry(
            row1_frame,
            textvariable=self.academic_year_var,
            font=get_arabic_font(size=12),
            width=15
        )
        self.academic_year_entry.pack(side='right')
        
        # الصف الثاني - أزرار التحكم
        row2_frame = tk.Frame(control_frame, bg='#ecf0f1')
        row2_frame.pack(fill='x', padx=10, pady=10)
        
        # زر تحميل الطلاب
        load_btn = create_arabic_button(
            row2_frame,
            "تحميل الطلاب",
            command=self.load_students,
            bg='#3498db',
            fg='white',
            width=15
        )
        load_btn.pack(side='right', padx=(0, 10))
        
        # زر حفظ جميع الدرجات
        save_all_btn = create_arabic_button(
            row2_frame,
            "حفظ جميع الدرجات",
            command=self.save_all_grades,
            bg='#27ae60',
            fg='white',
            width=15
        )
        save_all_btn.pack(side='right', padx=(0, 10))
        
        # زر إضافة مادة جديدة
        add_subject_btn = create_arabic_button(
            row2_frame,
            "إضافة مادة",
            command=self.add_new_subject,
            bg='#f39c12',
            fg='white',
            width=15
        )
        add_subject_btn.pack(side='left')
        
        # تحميل الشعب
        self.load_sections()

        # التحقق من الطالب المحدد بعد إنشاء العناصر
        self.window.after(100, self.check_selected_student)
        
    def create_grades_table(self, parent):
        """إنشاء جدول الدرجات"""
        table_frame = tk.LabelFrame(
            parent,
            text="جدول الدرجات",
            font=get_arabic_font(size=14, weight='bold'),
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        table_frame.pack(fill='both', expand=True)
        
        # إنشاء إطار للجدول مع شريط التمرير
        table_container = tk.Frame(table_frame, bg='#ecf0f1')
        table_container.pack(fill='both', expand=True, padx=10, pady=10)
        
        # إنشاء Canvas للتمرير الأفقي والعمودي
        self.canvas = tk.Canvas(table_container, bg='white')
        self.scrollable_frame = tk.Frame(self.canvas, bg='white')
        
        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_container, orient="vertical", command=self.canvas.yview)
        self.canvas.configure(yscrollcommand=v_scrollbar.set)
        
        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_container, orient="horizontal", command=self.canvas.xview)
        self.canvas.configure(xscrollcommand=h_scrollbar.set)
        
        # ربط الإطار القابل للتمرير بالـ Canvas
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        
        # تخطيط العناصر
        self.canvas.pack(side="left", fill="both", expand=True)
        v_scrollbar.pack(side="right", fill="y")
        h_scrollbar.pack(side="bottom", fill="x")
        
        # ربط عجلة الماوس بالتمرير
        self._bind_mousewheel()

    def _bind_mousewheel(self):
        # دعم التمرير على جميع الأنظمة
        self.canvas.bind_all("<MouseWheel>", self._on_mousewheel)
        self.canvas.bind_all("<Shift-MouseWheel>", self._on_mousewheel)

    def _on_mousewheel(self, event):
        """معالجة التمرير بعجلة الماوس"""
        # دعم التمرير الأفقي مع Shift
        if hasattr(event, 'state') and event.state & 0x1:
            self.canvas.xview_scroll(int(-1*(event.delta/120)), "units")
        else:
            self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def load_sections(self):
        """تحميل الشعب"""
        try:
            sections = self.db_manager.get_all_sections()
            self.section_combo['values'] = sections
            if sections:
                self.section_combo.set(sections[0])
                self.on_section_changed()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الشعب: {str(e)}")
    
    def on_section_changed(self, event=None):
        """عند تغيير الشعبة"""
        section = self.section_var.get()
        if not section:
            return
        
        try:
            # تحميل المواد للشعبة المحددة
            subjects = self.db_manager.get_subjects_by_section(section)
            subject_names = [subject[1] for subject in subjects]
            self.subject_combo['values'] = subject_names
            
            if subject_names:
                self.subject_combo.set(subject_names[0])
                self.on_subject_changed()
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل المواد: {str(e)}")
    
    def on_subject_changed(self, event=None):
        """عند تغيير المادة"""
        # مسح الجدول الحالي
        self.clear_grades_table()
    
    def load_students(self):
        """تحميل الطلاب وإنشاء جدول الدرجات"""
        section = self.section_var.get()
        subject = self.subject_var.get()
        
        if not section or not subject:
            messagebox.showerror("خطأ", "يرجى اختيار الشعبة والمادة")
            return
        
        try:
            # تحميل الطلاب
            self.current_students = self.db_manager.get_students_by_section(section)
            
            if not self.current_students:
                messagebox.showinfo("تنبيه", "لا توجد طلاب في هذه الشعبة")
                return
            
            # إنشاء جدول الدرجات
            self.create_grades_input_table()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الطلاب: {str(e)}")
    
    def create_grades_input_table(self):
        """إنشاء جدول إدخال الدرجات"""
        # مسح المحتوى السابق
        self.clear_grades_table()
        
        # إنشاء رؤوس الأعمدة
        headers = [
            "الاسم الكامل", "رقم الجلوس",
            "الفصل الأول - عمل", "الفصل الأول - امتحان",
            "الفصل الثاني - عمل", "الفصل الثاني - امتحان",
            "مجموع العمل", "مجموع الامتحان", "المجموع النهائي",
            "النتيجة", "الدور الثاني"
        ]
        
        # إنشاء رؤوس الأعمدة
        for col, header in enumerate(headers):
            header_label = tk.Label(
                self.scrollable_frame,
                text=header,
                font=get_arabic_font(size=10, weight='bold'),
                bg='#34495e',
                fg='white',
                relief='solid',
                bd=1,
                width=12,
                height=2
            )
            header_label.grid(row=0, column=col, sticky='nsew', padx=1, pady=1)
        
        # متغيرات لحفظ حقول الإدخال
        self.grade_entries = {}
        self.result_labels = {}
        self.second_round_entries = {}
        
        # إنشاء صفوف الطلاب
        for row, student in enumerate(self.current_students, start=1):
            student_id = student[0]
            student_name = student[1]
            seat_number = student[3]
            
            # الاسم الكامل
            tk.Label(
                self.scrollable_frame,
                text=student_name,
                font=get_arabic_font(size=10),
                bg='white',
                relief='solid',
                bd=1,
                width=15,
                anchor='e'
            ).grid(row=row, column=0, sticky='nsew', padx=1, pady=1)
            
            # رقم الجلوس
            tk.Label(
                self.scrollable_frame,
                text=seat_number,
                font=get_arabic_font(size=10),
                bg='white',
                relief='solid',
                bd=1,
                width=12
            ).grid(row=row, column=1, sticky='nsew', padx=1, pady=1)
            
            # حقول إدخال الدرجات
            self.grade_entries[student_id] = {}
            
            grade_fields = ['sem1_work', 'sem1_exam', 'sem2_work', 'sem2_exam']
            
            for col, field in enumerate(grade_fields, start=2):
                var = tk.StringVar()
                entry = tk.Entry(
                    self.scrollable_frame,
                    textvariable=var,
                    font=get_arabic_font(size=10),
                    width=10,
                    justify='center'
                )
                entry.grid(row=row, column=col, sticky='nsew', padx=1, pady=1)
                entry.bind('<KeyRelease>', lambda e, sid=student_id: self.calculate_totals(sid))
                
                self.grade_entries[student_id][field] = var
            
            # مجموع العمل (محسوب تلقائياً)
            total_work_label = tk.Label(
                self.scrollable_frame,
                text="0",
                font=get_arabic_font(size=10),
                bg='#ecf0f1',
                relief='solid',
                bd=1,
                width=12
            )
            total_work_label.grid(row=row, column=6, sticky='nsew', padx=1, pady=1)
            self.grade_entries[student_id]['total_work_label'] = total_work_label
            
            # مجموع الامتحان (محسوب تلقائياً)
            total_exam_label = tk.Label(
                self.scrollable_frame,
                text="0",
                font=get_arabic_font(size=10),
                bg='#ecf0f1',
                relief='solid',
                bd=1,
                width=12
            )
            total_exam_label.grid(row=row, column=7, sticky='nsew', padx=1, pady=1)
            self.grade_entries[student_id]['total_exam_label'] = total_exam_label
            
            # المجموع النهائي (محسوب تلقائياً)
            final_total_label = tk.Label(
                self.scrollable_frame,
                text="0",
                font=get_arabic_font(size=10),
                bg='#ecf0f1',
                relief='solid',
                bd=1,
                width=12
            )
            final_total_label.grid(row=row, column=8, sticky='nsew', padx=1, pady=1)
            self.grade_entries[student_id]['final_total_label'] = final_total_label
            
            # النتيجة (محسوبة تلقائياً)
            result_label = tk.Label(
                self.scrollable_frame,
                text="",
                font=get_arabic_font(size=10, weight='bold'),
                bg='white',
                relief='solid',
                bd=1,
                width=12
            )
            result_label.grid(row=row, column=9, sticky='nsew', padx=1, pady=1)
            self.result_labels[student_id] = result_label
            
            # الدور الثاني
            second_round_var = tk.StringVar()
            second_round_entry = tk.Entry(
                self.scrollable_frame,
                textvariable=second_round_var,
                font=get_arabic_font(size=10),
                width=10,
                justify='center',
                state='disabled'
            )
            second_round_entry.grid(row=row, column=10, sticky='nsew', padx=1, pady=1)
            second_round_entry.bind('<KeyRelease>', lambda e, sid=student_id: self.calculate_second_round(sid))
            
            self.second_round_entries[student_id] = second_round_var
            self.grade_entries[student_id]['second_round_entry'] = second_round_entry
        
        # تحديث منطقة التمرير
        self.scrollable_frame.update_idletasks()
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
    
    def calculate_totals(self, student_id):
        """حساب المجاميع للطالب"""
        try:
            # الحصول على الدرجات
            sem1_work = float(self.grade_entries[student_id]['sem1_work'].get() or 0)
            sem1_exam = float(self.grade_entries[student_id]['sem1_exam'].get() or 0)
            sem2_work = float(self.grade_entries[student_id]['sem2_work'].get() or 0)
            sem2_exam = float(self.grade_entries[student_id]['sem2_exam'].get() or 0)
            
            # حساب المجاميع
            total_work = sem1_work + sem2_work
            total_exam = sem1_exam + sem2_exam
            final_total = total_work + total_exam
            
            # تحديث التسميات
            self.grade_entries[student_id]['total_work_label'].configure(text=str(total_work))
            self.grade_entries[student_id]['total_exam_label'].configure(text=str(total_exam))
            self.grade_entries[student_id]['final_total_label'].configure(text=str(final_total))
            
            # تحديد النتيجة
            if total_exam >= 24 and final_total >= 50:
                result_text = "ناجح"
                result_color = '#27ae60'
                # تعطيل حقل الدور الثاني
                self.grade_entries[student_id]['second_round_entry'].configure(state='disabled')
            else:
                result_text = "راسب"
                result_color = '#e74c3c'
                # تفعيل حقل الدور الثاني
                self.grade_entries[student_id]['second_round_entry'].configure(state='normal')
            
            self.result_labels[student_id].configure(text=result_text, fg=result_color)
            
        except ValueError:
            # في حالة إدخال قيم غير صحيحة
            pass
    
    def calculate_second_round(self, student_id):
        """حساب نتيجة الدور الثاني"""
        try:
            # الحصول على درجة الدور الثاني
            second_round = float(self.second_round_entries[student_id].get() or 0)
            
            # الحصول على مجموع العمل
            total_work = float(self.grade_entries[student_id]['total_work_label'].cget('text'))
            
            # حساب المجموع النهائي مع الدور الثاني
            final_with_second = total_work + second_round
            
            # تحديد النتيجة
            if final_with_second >= 50:
                result_text = "ناجح (دور ثاني)"
                result_color = '#f39c12'
            else:
                result_text = "راسب"
                result_color = '#e74c3c'
            
            self.result_labels[student_id].configure(text=result_text, fg=result_color)
            
        except ValueError:
            pass
    
    def save_all_grades(self):
        """حفظ جميع الدرجات"""
        section = self.section_var.get()
        subject = self.subject_var.get()
        academic_year = self.academic_year_var.get()
        
        if not section or not subject:
            messagebox.showerror("خطأ", "يرجى اختيار الشعبة والمادة")
            return
        
        if not self.current_students:
            messagebox.showerror("خطأ", "لا توجد بيانات طلاب لحفظها")
            return
        
        try:
            # الحصول على معرف المادة
            subjects = self.db_manager.get_subjects_by_section(section)
            subject_id = None
            for subj in subjects:
                if subj[1] == subject:
                    subject_id = subj[0]
                    break
            
            if not subject_id:
                messagebox.showerror("خطأ", "لم يتم العثور على المادة")
                return
            
            saved_count = 0
            
            for student in self.current_students:
                student_id = student[0]
                
                # التحقق من وجود بيانات الدرجات
                if student_id not in self.grade_entries:
                    continue
                
                try:
                    # الحصول على الدرجات
                    grades_data = {
                        'semester1_work': float(self.grade_entries[student_id]['sem1_work'].get() or 0),
                        'semester1_exam': float(self.grade_entries[student_id]['sem1_exam'].get() or 0),
                        'semester2_work': float(self.grade_entries[student_id]['sem2_work'].get() or 0),
                        'semester2_exam': float(self.grade_entries[student_id]['sem2_exam'].get() or 0)
                    }
                    
                    # حفظ الدرجات الأساسية
                    self.db_manager.save_grades(student_id, subject_id, grades_data, academic_year)
                    
                    # حفظ درجة الدور الثاني إذا وجدت
                    second_round_score = self.second_round_entries[student_id].get()
                    if second_round_score and second_round_score.strip():
                        self.db_manager.save_second_round_grade(
                            student_id, 
                            subject_id, 
                            float(second_round_score)
                        )
                    
                    saved_count += 1
                    
                except ValueError as ve:
                    messagebox.showerror("خطأ", f"خطأ في بيانات الطالب {student[1]}: قيم غير صحيحة")
                    continue
                except Exception as e:
                    messagebox.showerror("خطأ", f"خطأ في حفظ بيانات الطالب {student[1]}: {str(e)}")
                    continue
            
            messagebox.showinfo("نجح الحفظ", f"تم حفظ درجات {saved_count} طالب بنجاح")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الدرجات: {str(e)}")
    
    def add_new_subject(self):
        """إضافة مادة جديدة"""
        section = self.section_var.get()
        if not section:
            messagebox.showerror("خطأ", "يرجى اختيار الشعبة أولاً")
            return
        
        dialog = tk.Toplevel(self.window)
        dialog.title("إضافة مادة جديدة")
        dialog.geometry("400x200")
        dialog.configure(bg='#ecf0f1')
        dialog.transient(self.window)
        dialog.grab_set()
        # توسيط النافذة
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - 200
        y = (dialog.winfo_screenheight() // 2) - 100
        dialog.geometry(f'400x200+{x}+{y}')
        
        # العنوان
        tk.Label(
            dialog,
            text="إضافة مادة جديدة",
            font=get_arabic_font(size=16, weight='bold'),
            bg='#ecf0f1',
            fg='#2c3e50'
        ).pack(pady=20)
        
        # حقل الإدخال
        tk.Label(
            dialog,
            text="اسم المادة:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1'
        ).pack(pady=(0, 10))
        
        subject_name_var = tk.StringVar()
        subject_entry = RTLEntry(
            dialog,
            textvariable=subject_name_var,
            font=get_arabic_font(size=12),
            width=30
        )
        subject_entry.pack(pady=(0, 20))
        subject_entry.focus()
        
        # الأزرار
        buttons_frame = tk.Frame(dialog, bg='#ecf0f1')
        buttons_frame.pack()
        
        def save_subject():
            subject_name = subject_name_var.get().strip()
            if not subject_name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم المادة")
                return
            
            try:
                self.db_manager.add_subject(subject_name, section)
                messagebox.showinfo("نجح", "تم إضافة المادة بنجاح")
                
                # تحديث قائمة المواد
                self.on_section_changed()
                
                dialog.destroy()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في إضافة المادة: {str(e)}")
        
        create_arabic_button(
            buttons_frame,
            "حفظ",
            command=save_subject,
            bg='#27ae60',
            fg='white',
            width=10
        ).pack(side='right', padx=(0, 10))
        
        create_arabic_button(
            buttons_frame,
            "إلغاء",
            command=dialog.destroy,
            bg='#e74c3c',
            fg='white',
            width=10
        ).pack(side='right')
    
    def clear_grades_table(self):
        """مسح جدول الدرجات"""
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()
        
        self.grade_entries = {}
        self.result_labels = {}
        self.second_round_entries = {}
    
    # ----------------------
    # دعم التشغيل المستقل للاختبار
    # ----------------------
    def run(self):
        """تشغيل النافذة (للاختبار أو التشغيل المستقل)"""
        self.window.mainloop()

    def close_window(self):
        """إغلاق النافذة بشكل آمن"""
        try:
            self.window.grab_release()
        except Exception:
            pass
        self.window.destroy()

# نقطة تشغيل مستقلة لاختبار النافذة
if __name__ == "__main__":
    class DummyDB:
        def get_all_sections(self):
            return ["شعبة أ", "شعبة ب"]
        def get_subjects_by_section(self, section):
            return [(1, "رياضيات"), (2, "حاسوب")]
        def get_students_by_section(self, section):
            return [
                (1, "أحمد محمد", "ذكر", "101"),
                (2, "سارة علي", "أنثى", "102"),
            ]
        def save_grades(self, student_id, subject_id, grades_data, academic_year):
            print(f"تم حفظ درجات الطالب {student_id} للمادة {subject_id}: {grades_data} للسنة {academic_year}")
        def save_second_round_grade(self, student_id, subject_id, grade):
            print(f"تم حفظ درجة الدور الثاني للطالب {student_id}: {grade}")
        def add_subject(self, subject_name, section):
            print(f"تمت إضافة مادة {subject_name} للشعبة {section}")

    root = tk.Tk()
    root.withdraw()
    app = GradeEntryWindow(root, DummyDB())
    app.run()
