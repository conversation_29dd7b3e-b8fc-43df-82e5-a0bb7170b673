# نظام محاسبة مكتبة التميز
## Excellence Bookstore Accounting System

**المنظومة الإلكترونية - إعداد محمد مطرود**  
*Electronic System - Prepared by <PERSON> Matroud*

---

## 📖 نظرة عامة

نظام محاسبة متكامل ومتطور مصمم خصيصاً لمكتبة التميز، يوفر حلولاً شاملة لإدارة المبيعات، المصاريف، الديون، والتقارير المالية مع دعم كامل للغة العربية والكتابة من اليمين لليسار (RTL).

### ✨ الميزات الرئيسية

- 🛒 **نقطة البيع (POS)** - واجهة مبيعات احترافية وسهلة الاستخدام
- 📦 **إدارة الأصناف** - منتجات بأسعار ثابتة ومتغيرة (مثل تأمين السيارات)
- 💰 **إدارة المصاريف** - تتبع شامل لجميع المصروفات
- 📊 **إدارة الديون** - نظام أقساط متطور مع تتبع المدفوعات
- 📈 **التقارير المالية** - تقارير مبيعات وأرباح تفصيلية
- 🖨️ **طباعة احترافية** - فواتير وتقارير جاهزة للطباعة
- 💾 **قاعدة بيانات آمنة** - حفظ موثوق مع نسخ احتياطي تلقائي
- 🌐 **دعم عربي كامل** - واجهة RTL مع خطوط عربية محسنة

---

## 🚀 التشغيل السريع

### الطريقة الأولى (الأسهل):
```bash
# انقر نقراً مزدوجاً على
run_system.bat
```

### الطريقة الثانية:
```bash
python main.py
```

---

## 📋 المتطلبات

### الحد الأدنى:
- **Python**: 3.6 أو أحدث
- **نظام التشغيل**: Windows 7+, macOS 10.12+, Linux
- **الذاكرة**: 1 GB RAM
- **مساحة القرص**: 200 MB

### المستحسن:
- **Python**: 3.9 أو أحدث
- **نظام التشغيل**: Windows 10/11
- **الذاكرة**: 4 GB RAM أو أكثر
- **مساحة القرص**: 1 GB

---

## 🏗️ هيكل النظام

```
excellence_bookstore_system/
├── 📄 main.py                    # الملف الرئيسي
├── 📄 run_system.bat             # ملف تشغيل Windows
├── 📄 requirements.txt           # متطلبات Python
├── 📄 README.md                  # دليل النظام
│
├── 📂 database/                  # قاعدة البيانات
│   ├── __init__.py
│   └── db_manager.py             # مدير قاعدة البيانات المتطور
│
├── 📂 gui/                       # واجهة المستخدم
│   ├── __init__.py
│   ├── main_window.py            # النافذة الرئيسية
│   ├── pos_window.py             # نافذة نقطة البيع
│   ├── products_window.py        # إدارة الأصناف (قريباً)
│   ├── expenses_window.py        # إدارة المصاريف (قريباً)
│   ├── debts_window.py           # إدارة الديون (قريباً)
│   └── reports_window.py         # التقارير المالية (قريباً)
│
├── 📂 utils/                     # أدوات مساعدة
│   ├── __init__.py
│   ├── arabic_support.py         # دعم اللغة العربية
│   └── config.py                 # إدارة الإعدادات
│
├── 📂 reports/                   # تقارير النظام
│   └── (ملفات التقارير)
│
├── 📂 resources/                 # موارد النظام
│   ├── icons/                    # أيقونات التطبيق
│   ├── templates/                # قوالب الطباعة
│   └── fonts/                    # خطوط عربية
│
├── 📂 data/                      # بيانات النظام
│   ├── excellence_bookstore.db   # قاعدة البيانات الرئيسية
│   ├── app_config.json           # إعدادات التطبيق
│   └── backups/                  # النسخ الاحتياطية
│
└── 📂 docs/                      # الوثائق
    └── user_guide.md             # دليل المستخدم
```

---

## 🛒 نقطة البيع (POS)

### الميزات الأساسية:
- ✅ **توليد رقم الفاتورة تلقائياً** مع بادئة قابلة للتخصيص
- ✅ **اختيار الأصناف** من قائمة جاهزة أو إضافة صنف جديد
- ✅ **أسعار متغيرة** لخدمات مثل تأمين السيارات
- ✅ **حساب تلقائي** للمجموع مع دعم الخصم
- ✅ **طرق دفع متعددة**: نقدي، بطاقة، آجلة
- ✅ **دفع جزئي** مع حساب المتبقي وإنشاء مديونية تلقائياً
- ✅ **معلومات العميل** (الاسم، الهاتف، البريد الإلكتروني)

### واجهة المستخدم:
- 🎨 **تصميم ثلاثي الأقسام**: المنتجات، الفاتورة، الدفع
- 🔍 **بحث سريع** في المنتجات
- ➕ **إضافة منتجات جديدة** أثناء البيع
- 🗑️ **تعديل وحذف** عناصر الفاتورة
- 💾 **حفظ وطباعة** الفواتير

---

## 📦 إدارة الأصناف

### أنواع المنتجات:
- **منتجات بسعر ثابت**: قرطاسية، كتب، أدوات مكتبية
- **منتجات بسعر متغير**: تأمين سيارات، خدمات استشارية
- **تصنيفات مرنة**: قرطاسية، تأمين، خدمات، إلخ

### الإدارة:
- ✅ إضافة وتعديل وحذف المنتجات
- ✅ تحديد السعر الافتراضي مع إمكانية التعديل
- ✅ إدارة الباركود والأوصاف
- ✅ تصنيف المنتجات حسب الفئات

---

## 💸 إدارة المصاريف

### التسجيل:
- 📝 **إدخال المصروف**: البند، المبلغ، التاريخ
- 🏷️ **تصنيف المصاريف**: إيجار، كهرباء، رواتب، إلخ
- 💳 **طريقة الدفع**: نقدي، بطاقة، تحويل
- 📝 **ملاحظات إضافية** لكل مصروف

### التقارير:
- 📊 **تقرير يومي** للمصاريف
- 📈 **تقرير شهري** مفصل
- 🖨️ **طباعة وتصدير** التقارير

---

## 📋 إدارة الديون

### النظام المتطور:
- 💰 **تسجيل تلقائي** للديون من الفواتير غير المسددة
- 📊 **عرض شامل**: رقم الفاتورة، العميل، المبلغ، المتبقي
- 💳 **دفعات جزئية** مع تحديث تلقائي للحالة
- 📅 **تواريخ الاستحقاق** والمتابعة
- 🔄 **حالات متعددة**: مستحق، مسدد جزئياً، مسدد بالكامل

### المتابعة:
- 📋 **كشف ديون شامل** قابل للطباعة
- 🔔 **تنبيهات** للديون المتأخرة
- 📈 **إحصائيات** الديون والتحصيل

---

## 📈 التقارير المالية

### تقارير المبيعات:
- 📊 **تقرير يومي**: عدد الفواتير، المبيعات، المحصل
- 📈 **تقرير شهري**: مبيعات مفصلة بالفترات
- 🎯 **تقرير سنوي**: أداء المبيعات السنوي

### تقارير الأرباح:
- 💰 **الربح الشهري**: المبيعات - المصاريف
- 📊 **تحليل الأرباح** بالفترات الزمنية
- 📈 **مؤشرات الأداء** المالي

### أرشيف الفواتير:
- 📋 **أرشيف كامل** لجميع الفواتير
- 🔍 **بحث متقدم** بالتاريخ والعميل
- 🖨️ **طباعة وتصدير** الفواتير

---

## 🖨️ الطباعة والتصدير

### فواتير احترافية:
- 📄 **ورقة A4** جاهزة للطباعة
- 🏪 **شعار المكتبة** وبيانات التواصل
- 📋 **تفاصيل شاملة**: الأصناف، الكميات، الأسعار
- 💰 **الإجماليات**: المجموع، الخصم، المدفوع، المتبقي
- ✅ **مكان للختم** وختم الإدارة
- 📝 **المنظومة الإلكترونية - إعداد محمد مطرود**

### تصدير البيانات:
- 📊 **PDF** للتقارير والفواتير
- 📈 **Excel** للبيانات المالية
- 💾 **نسخ احتياطي** شامل

---

## ⚙️ الإعدادات والتخصيص

### إعدادات المكتبة:
- 🏪 **اسم المكتبة**: مكتبة التميز
- 📍 **العنوان** ومعلومات التواصل
- 📞 **أرقام الهاتف** والبريد الإلكتروني
- 🖼️ **الشعار** والهوية البصرية

### إعدادات النظام:
- 🔢 **بادئة الفاتورة** وترقيم تلقائي
- 💱 **العملة** ومعدل الضريبة
- 🎨 **واجهة المستخدم** والخطوط
- 💾 **النسخ الاحتياطي** التلقائي

---

## 🔒 الأمان والنسخ الاحتياطي

### حماية البيانات:
- 🗄️ **قاعدة بيانات SQLite** موثوقة
- 🔐 **تشفير البيانات** الحساسة
- 📝 **سجل العمليات** الشامل
- 🔄 **استرداد البيانات** عند الأخطاء

### النسخ الاحتياطي:
- ⏰ **نسخ تلقائي** منتظم
- 💾 **نسخ يدوي** عند الحاجة
- 📁 **إدارة النسخ** وحذف القديم
- 🔄 **استعادة سريعة** للبيانات

---

## 🌐 الدعم العربي

### اللغة والخطوط:
- 🔤 **خطوط عربية محسنة** تلقائياً
- ↔️ **كتابة من اليمين لليسار** (RTL)
- 🎨 **واجهة عربية كاملة** مع تخطيط مناسب
- 🔢 **أرقام عربية وإنجليزية** حسب السياق

### التوافق:
- 🖥️ **Windows** مع دعم كامل للعربية
- 🍎 **macOS** و **Linux** مدعومان
- ⌨️ **لوحة المفاتيح العربية** مدعومة
- 🖨️ **طباعة عربية** صحيحة

---

## 📞 الدعم والمساعدة

### المطور:
**محمد مطرود**  
المنظومة الإلكترونية  
Electronic System

### الميزات المستقبلية:
- 📱 **تطبيق موبايل** مصاحب
- ☁️ **نسخ سحابي** للبيانات
- 📊 **تحليلات متقدمة** للمبيعات
- 🔔 **تنبيهات ذكية** للمخزون والديون

---

## 📄 الترخيص

هذا النظام مطور خصيصاً لمكتبة التميز  
**المنظومة الإلكترونية - إعداد محمد مطرود**

جميع الحقوق محفوظة © 2024

---

*نظام محاسبة متكامل ومتطور لإدارة أعمالك بكفاءة واحترافية*
