تعليمات تشغيل نظام إدارة الطلاب
=====================================

طرق التشغيل:
-----------

1. الطريقة الأسهل (Windows):
   - انقر نقراً مزدوجاً على ملف: run.bat

2. بديل للطريقة الأولى:
   - انقر نقراً مزدوجاً على: start_student_system.bat

3. من PowerShell:
   - انقر بالزر الأيمن على start_system.ps1
   - اختر "Run with PowerShell"

4. من سطر الأوامر:
   - افتح Command Prompt أو PowerShell
   - انتقل إلى مجلد البرنامج
   - اكتب: python run_student_system.py

5. تشغيل مباشر:
   - انتقل إلى مجلد student_management_system
   - اكتب: python main.py

المتطلبات:
----------
- Python 3.6 أو أحدث
- جميع المكتبات مثبتة افتراضياً مع Python

حل المشاكل الشائعة:
==================

🔧 إذا لم يعمل البرنامج:
------------------------

الخطوة 1: تشغيل أداة التشخيص
python تشخيص_المشاكل.py

الخطوة 2: تشغيل الإصلاح السريع
python إصلاح_سريع.py

الخطوة 3: إعادة المحاولة
python run_student_system.py

🚫 مشاكل محددة وحلولها:
-----------------------

المشكلة: "python is not recognized"
الحل:
1. تأكد من تثبيت Python من https://python.org
2. أثناء التثبيت، اختر "Add Python to PATH"
3. أعد تشغيل الكمبيوتر
4. جرب مرة أخرى

المشكلة: "No module named 'tkinter'"
الحل:
1. أعد تثبيت Python مع tkinter
2. على Linux: sudo apt-get install python3-tk
3. على macOS: brew install python-tk

المشكلة: لا تظهر النافذة
الحل:
1. تحقق من رسائل الخطأ في سطر الأوامر
2. شغل البرنامج كمدير (Run as Administrator)
3. تأكد من عدم حجب برنامج مكافح الفيروسات

المشكلة: خطأ في قاعدة البيانات
الحل:
1. احذف مجلد data بالكامل
2. أعد تشغيل البرنامج
3. سيتم إنشاء قاعدة بيانات جديدة

المشكلة: خطأ في الخطوط العربية
الحل:
1. تأكد من وجود خطوط عربية على النظام
2. على Windows: تأكد من وجود خط Tahoma
3. أعد تشغيل البرنامج

المشكلة: خطأ في الصلاحيات
الحل:
1. شغل البرنامج كمدير
2. تأكد من صلاحيات الكتابة في المجلد
3. انقل البرنامج إلى مجلد آخر

🔍 خطوات التشخيص المتقدم:
-------------------------

1. فحص Python:
   python --version

2. فحص tkinter:
   python -c "import tkinter; print('OK')"

3. فحص الملفات:
   تأكد من وجود جميع الملفات في المجلد

4. فحص الاستيرادات:
   cd student_management_system
   python -c "from main import main; print('OK')"

الاستخدام الأول:
---------------
1. شغل البرنامج
2. اذهب إلى "إعدادات النظام"
3. أدخل معلومات المعهد
4. أضف الشعب المطلوبة
5. ابدأ بإدخال بيانات الطلاب

النسخ الاحتياطي:
---------------
- اذهب إلى "إعدادات النظام" > "النسخ الاحتياطي"
- حدد مجلد الحفظ
- اضغط "إنشاء نسخة احتياطية"

📞 للمساعدة الإضافية:
--------------------
1. راجع ملف README.md للتفاصيل الكاملة
2. شغل أداة التشخيص للحصول على تقرير مفصل
3. تأكد من تحديث Python إلى أحدث إصدار
