# متطلبات نظام محاسبة مكتبة التميز
# Excellence Bookstore Accounting System Requirements
# المنظومة الإلكترونية - إعداد محمد مطرود

# المكتبات الأساسية (مثبتة افتراضياً مع Python 3.6+)
# Core libraries (included with Python 3.6+ by default)

# tkinter - واجهة المستخدم الرسومية
# GUI framework (included with Python)

# sqlite3 - قاعدة البيانات
# Database engine (included with Python)

# datetime - التعامل مع التواريخ والأوقات
# Date and time handling (included with Python)

# json - التعامل مع ملفات JSON
# JSON file handling (included with Python)

# os - عمليات نظام التشغيل
# Operating system interface (included with Python)

# sys - معلومات النظام
# System-specific parameters (included with Python)

# platform - معلومات المنصة
# Platform identification (included with Python)

# re - التعبيرات النمطية
# Regular expressions (included with Python)

# shutil - عمليات الملفات المتقدمة
# High-level file operations (included with Python)

# threading - البرمجة متعددة الخيوط
# Threading support (included with Python)

# المكتبات الاختيارية للميزات المتقدمة:
# Optional libraries for advanced features:

# reportlab>=3.6.0
# For PDF generation and advanced printing

# pillow>=8.0.0
# For image processing and logo handling

# openpyxl>=3.0.0
# For Excel export functionality

# ملاحظات:
# Notes:

# 1. جميع المكتبات الأساسية مثبتة افتراضياً مع Python 3.6+
#    All core libraries are included with Python 3.6+ by default

# 2. لا حاجة لتثبيت مكتبات إضافية للتشغيل الأساسي
#    No additional libraries needed for basic operation

# 3. للتشغيل على Windows:
#    To run on Windows:
#    - انقر نقراً مزدوجاً على run_system.bat
#    - Double-click run_system.bat

# 4. للتشغيل من سطر الأوامر:
#    To run from command line:
#    python main.py

# 5. متطلبات النظام:
#    System requirements:
#    - Python 3.6 أو أحدث / Python 3.6 or newer
#    - Windows 7/8/10/11, macOS 10.12+, أو Linux
#    - 200 MB مساحة فارغة على القرص الصلب
#    - 1 GB ذاكرة وصول عشوائي (RAM)

# 6. للحصول على الدعم:
#    For support:
#    المنظومة الإلكترونية - إعداد محمد مطرود
#    Electronic System - Prepared by Mohammed Matroud

# إصدار Python المطلوب:
# Required Python version:
# python_requires>=3.6
