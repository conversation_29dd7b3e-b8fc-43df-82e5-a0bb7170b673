#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النافذة الرئيسية المحسنة لنظام إدارة الطلاب
Enhanced Main Window for Student Management System

معهد المتوسط للمهن الشاملة - اجخرة
الإصدار المحسن 2.0
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import sys
import os

# إضافة مسار المكتبات
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, current_dir)

from utils.arabic_support import *
from utils.helpers import *

class MainWindow:
    """النافذة الرئيسية المحسنة"""
    
    def __init__(self, root, db_manager, settings):
        """تهيئة النافذة الرئيسية"""
        self.root = root
        self.db_manager = db_manager
        self.settings = settings
        self.selected_student = None
        
        # إعداد واجهة المستخدم
        self.setup_ui()
        
        # تحديث الإحصائيات
        self.update_statistics()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم الرئيسية"""
        # إطار العنوان الرئيسي
        self.create_header()
        
        # الإطار الرئيسي للمحتوى
        main_frame = tk.Frame(self.root, bg='#ecf0f1')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # إنشاء الأقسام الرئيسية
        self.create_quick_access_panel(main_frame)
        self.create_main_buttons(main_frame)
        self.create_statistics_panel(main_frame)
        
        # شريط الحالة
        self.create_status_bar()
    
    def create_header(self):
        """إنشاء رأس النافذة"""
        header_frame = tk.Frame(self.root, bg='#2c3e50', height=100)
        header_frame.pack(fill='x', pady=(0, 10))
        header_frame.pack_propagate(False)
        
        # العنوان الرئيسي
        title_label = tk.Label(
            header_frame,
            text="نظام إدارة الطلاب",
            font=get_arabic_font(size=28, weight='bold'),
            fg='white',
            bg='#2c3e50'
        )
        title_label.pack(pady=(15, 5))
        
        # العنوان الفرعي
        subtitle_label = tk.Label(
            header_frame,
            text=f"{self.settings.institute_name} - {self.settings.institute_location}",
            font=get_arabic_font(size=16),
            fg='#ecf0f1',
            bg='#2c3e50'
        )
        subtitle_label.pack()
        
        # السنة الدراسية الحالية
        year_label = tk.Label(
            header_frame,
            text=f"السنة الدراسية: {self.settings.current_academic_year}",
            font=get_arabic_font(size=12),
            fg='#bdc3c7',
            bg='#2c3e50'
        )
        year_label.pack(pady=(5, 0))
    
    def create_quick_access_panel(self, parent):
        """إنشاء لوحة الوصول السريع"""
        quick_frame = tk.LabelFrame(
            parent,
            text="الوصول السريع",
            font=get_arabic_font(size=14, weight='bold'),
            bg='#ecf0f1',
            fg='#2c3e50',
            padx=10,
            pady=10
        )
        quick_frame.pack(fill='x', pady=(0, 15))
        
        # إطار الأزرار السريعة
        buttons_frame = tk.Frame(quick_frame, bg='#ecf0f1')
        buttons_frame.pack(fill='x')
        
        # أزرار الوصول السريع
        quick_buttons = [
            {
                'text': 'طالب جديد',
                'icon': '👤',
                'command': self.quick_add_student,
                'color': '#3498db'
            },
            {
                'text': 'بحث سريع',
                'icon': '🔍',
                'command': self.quick_search,
                'color': '#f39c12'
            },
            {
                'text': 'إحصائيات',
                'icon': '📊',
                'command': self.show_statistics,
                'color': '#9b59b6'
            },
            {
                'text': 'نسخة احتياطية',
                'icon': '💾',
                'command': self.quick_backup,
                'color': '#27ae60'
            }
        ]
        
        for i, btn_data in enumerate(quick_buttons):
            btn = create_styled_button(
                buttons_frame,
                f"{btn_data['icon']} {btn_data['text']}",
                command=btn_data['command'],
                bg=btn_data['color'],
                fg='white',
                width=15
            )
            btn.grid(row=0, column=i, padx=5, pady=5, sticky='ew')
        
        # تكوين الأعمدة للتوسع المتساوي
        for i in range(len(quick_buttons)):
            buttons_frame.columnconfigure(i, weight=1)
    
    def create_main_buttons(self, parent):
        """إنشاء الأزرار الرئيسية"""
        buttons_frame = tk.LabelFrame(
            parent,
            text="الوظائف الرئيسية",
            font=get_arabic_font(size=14, weight='bold'),
            bg='#ecf0f1',
            fg='#2c3e50',
            padx=10,
            pady=10
        )
        buttons_frame.pack(fill='both', expand=True, pady=(0, 15))
        
        # إطار الشبكة للأزرار
        grid_frame = tk.Frame(buttons_frame, bg='#ecf0f1')
        grid_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # بيانات الأزرار الرئيسية
        main_buttons = [
            {
                'text': 'إدارة الطلاب',
                'icon': '👥',
                'command': self.open_student_management,
                'color': '#3498db',
                'description': 'إضافة وتعديل وحذف بيانات الطلاب',
                'row': 0, 'col': 0
            },
            {
                'text': 'إدارة الدرجات',
                'icon': '📝',
                'command': self.open_grade_management,
                'color': '#e74c3c',
                'description': 'إدخال وتعديل درجات الطلاب',
                'row': 0, 'col': 1
            },
            {
                'text': 'البحث والاستعلام',
                'icon': '🔍',
                'command': self.open_search_window,
                'color': '#f39c12',
                'description': 'البحث عن الطلاب وعرض بياناتهم',
                'row': 0, 'col': 2
            },
            {
                'text': 'الشهادات والتقارير',
                'icon': '📜',
                'command': self.open_certificates,
                'color': '#27ae60',
                'description': 'إنتاج الشهادات والتقارير',
                'row': 1, 'col': 0
            },
            {
                'text': 'بطاقات الجلوس',
                'icon': '🎫',
                'command': self.generate_seat_cards,
                'color': '#9b59b6',
                'description': 'إنتاج بطاقات أرقام الجلوس',
                'row': 1, 'col': 1
            },
            {
                'text': 'إعدادات النظام',
                'icon': '⚙️',
                'command': self.open_settings,
                'color': '#34495e',
                'description': 'إعدادات التطبيق والنظام',
                'row': 1, 'col': 2
            }
        ]
        
        # إنشاء الأزرار
        for btn_data in main_buttons:
            self.create_main_button(
                grid_frame,
                btn_data['text'],
                btn_data['icon'],
                btn_data['command'],
                btn_data['color'],
                btn_data['description'],
                btn_data['row'],
                btn_data['col']
            )
        
        # تكوين الشبكة للتوسع المتساوي
        for i in range(3):
            grid_frame.columnconfigure(i, weight=1)
        for i in range(2):
            grid_frame.rowconfigure(i, weight=1)
    
    def create_main_button(self, parent, text, icon, command, color, description, row, col):
        """إنشاء زر رئيسي مُنسق"""
        # إطار الزر
        button_frame = tk.Frame(
            parent,
            bg=color,
            relief='raised',
            bd=2,
            cursor='hand2'
        )
        button_frame.grid(
            row=row, column=col,
            padx=10, pady=10,
            sticky='nsew',
            ipadx=10, ipady=10
        )
        
        # ربط النقر بالإطار
        button_frame.bind('<Button-1>', lambda e: command())
        
        # الأيقونة
        icon_label = tk.Label(
            button_frame,
            text=icon,
            font=('Arial', 36),
            bg=color,
            fg='white'
        )
        icon_label.pack(pady=(15, 5))
        icon_label.bind('<Button-1>', lambda e: command())
        
        # النص الرئيسي
        text_label = tk.Label(
            button_frame,
            text=text,
            font=get_arabic_font(size=16, weight='bold'),
            bg=color,
            fg='white',
            wraplength=180
        )
        text_label.pack(pady=(0, 5))
        text_label.bind('<Button-1>', lambda e: command())
        
        # الوصف
        desc_label = tk.Label(
            button_frame,
            text=description,
            font=get_arabic_font(size=10),
            bg=color,
            fg='#ecf0f1',
            wraplength=180
        )
        desc_label.pack(pady=(0, 15))
        desc_label.bind('<Button-1>', lambda e: command())
        
        # تأثيرات التفاعل
        def on_enter(event):
            button_frame.configure(relief='sunken')
        
        def on_leave(event):
            button_frame.configure(relief='raised')
        
        button_frame.bind('<Enter>', on_enter)
        button_frame.bind('<Leave>', on_leave)
        
        # ربط جميع العناصر الفرعية بالأحداث
        for child in button_frame.winfo_children():
            child.bind('<Enter>', on_enter)
            child.bind('<Leave>', on_leave)
    
    def create_statistics_panel(self, parent):
        """إنشاء لوحة الإحصائيات"""
        stats_frame = tk.LabelFrame(
            parent,
            text="إحصائيات سريعة",
            font=get_arabic_font(size=14, weight='bold'),
            bg='#ecf0f1',
            fg='#2c3e50',
            padx=10,
            pady=10
        )
        stats_frame.pack(fill='x', pady=(0, 10))
        
        # إطار الإحصائيات
        self.stats_content = tk.Frame(stats_frame, bg='#ecf0f1')
        self.stats_content.pack(fill='x', padx=10, pady=5)
        
        # تحديث الإحصائيات
        self.update_statistics()
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = tk.Frame(self.root, bg='#34495e', height=35)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)
        
        # معلومات الحالة
        self.status_label = tk.Label(
            status_frame,
            text="جاهز",
            font=get_arabic_font(size=10),
            bg='#34495e',
            fg='white',
            anchor='w'
        )
        self.status_label.pack(side='left', padx=10, pady=8)
        
        # التاريخ والوقت
        self.time_label = tk.Label(
            status_frame,
            text="",
            font=get_arabic_font(size=10),
            bg='#34495e',
            fg='#bdc3c7',
            anchor='e'
        )
        self.time_label.pack(side='right', padx=10, pady=8)
        
        # تحديث الوقت
        self.update_time()
    
    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        
        # جدولة التحديث التالي
        self.root.after(1000, self.update_time)
    
    def update_statistics(self):
        """تحديث الإحصائيات"""
        # مسح المحتوى السابق
        for widget in self.stats_content.winfo_children():
            widget.destroy()
        
        try:
            # الحصول على الإحصائيات
            sections = self.db_manager.get_all_sections()
            total_students = 0
            
            stats_data = []
            for section in sections:
                students = self.db_manager.get_students_by_section(section)
                section_count = len(students)
                total_students += section_count
                stats_data.append((section, section_count))
            
            # عرض الإحصائيات الإجمالية
            total_label = tk.Label(
                self.stats_content,
                text=f"إجمالي الطلاب: {total_students} | عدد الشعب: {len(sections)}",
                font=get_arabic_font(size=12, weight='bold'),
                bg='#ecf0f1',
                fg='#2c3e50'
            )
            total_label.pack(pady=5)
            
            # عرض إحصائيات الشعب
            if stats_data:
                sections_frame = tk.Frame(self.stats_content, bg='#ecf0f1')
                sections_frame.pack(fill='x', pady=5)
                
                for i, (section, count) in enumerate(stats_data):
                    section_label = tk.Label(
                        sections_frame,
                        text=f"{section}: {count}",
                        font=get_arabic_font(size=10),
                        bg='#ecf0f1',
                        fg='#7f8c8d'
                    )
                    section_label.grid(row=0, column=i, padx=10)
                
                # تكوين الأعمدة
                for i in range(len(stats_data)):
                    sections_frame.columnconfigure(i, weight=1)
            
        except Exception as e:
            error_label = tk.Label(
                self.stats_content,
                text="خطأ في تحميل الإحصائيات",
                font=get_arabic_font(size=12),
                bg='#ecf0f1',
                fg='#e74c3c'
            )
            error_label.pack(pady=5)
            print(f"خطأ في تحديث الإحصائيات: {e}")
    
    def update_status(self, message):
        """تحديث رسالة الحالة"""
        self.status_label.config(text=message)
        self.root.update_idletasks()
    
    # وظائف الأزرار السريعة
    def quick_add_student(self):
        """إضافة طالب جديد سريع"""
        self.update_status("فتح نافذة إضافة طالب جديد...")
        self.open_student_management()
    
    def quick_search(self):
        """بحث سريع"""
        self.update_status("فتح نافذة البحث السريع...")
        self.open_search_window()
    
    def show_statistics(self):
        """عرض الإحصائيات التفصيلية"""
        self.update_status("عرض الإحصائيات التفصيلية...")
        # يمكن إضافة نافذة إحصائيات مفصلة هنا
        show_info_message("الإحصائيات", "سيتم إضافة نافذة الإحصائيات التفصيلية قريباً")
    
    def quick_backup(self):
        """إنشاء نسخة احتياطية سريعة"""
        self.update_status("إنشاء نسخة احتياطية...")
        try:
            success, result = self.db_manager.create_backup(
                description="نسخة احتياطية سريعة"
            )
            if success:
                show_success_message("نسخة احتياطية", f"تم إنشاء النسخة الاحتياطية بنجاح\n{result}")
            else:
                show_error_message("خطأ", f"فشل في إنشاء النسخة الاحتياطية\n{result}")
        except Exception as e:
            show_error_message("خطأ", f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
        finally:
            self.update_status("جاهز")
    
    # وظائف الأزرار الرئيسية
    def open_student_management(self):
        """فتح نافذة إدارة الطلاب"""
        self.update_status("فتح نافذة إدارة الطلاب...")
        try:
            from gui.student_entry import StudentEntryWindow
            StudentEntryWindow(self.root, self.db_manager, self.settings)
        except ImportError:
            show_error_message("خطأ", "نافذة إدارة الطلاب غير متوفرة")
        except Exception as e:
            show_error_message("خطأ", f"خطأ في فتح نافذة إدارة الطلاب: {str(e)}")
        finally:
            self.update_status("جاهز")
    
    def open_grade_management(self):
        """فتح نافذة إدارة الدرجات"""
        self.update_status("فتح نافذة إدارة الدرجات...")
        try:
            from gui.grade_entry import GradeEntryWindow
            GradeEntryWindow(self.root, self.db_manager, self.settings)
        except ImportError:
            show_error_message("خطأ", "نافذة إدارة الدرجات غير متوفرة")
        except Exception as e:
            show_error_message("خطأ", f"خطأ في فتح نافذة إدارة الدرجات: {str(e)}")
        finally:
            self.update_status("جاهز")
    
    def open_search_window(self):
        """فتح نافذة البحث"""
        self.update_status("فتح نافذة البحث...")
        try:
            from gui.student_search import StudentSearchWindow
            StudentSearchWindow(self.root, self.db_manager, self.settings)
        except ImportError:
            show_error_message("خطأ", "نافذة البحث غير متوفرة")
        except Exception as e:
            show_error_message("خطأ", f"خطأ في فتح نافذة البحث: {str(e)}")
        finally:
            self.update_status("جاهز")
    
    def open_certificates(self):
        """فتح نافذة الشهادات"""
        self.update_status("فتح نافذة الشهادات...")
        try:
            from gui.certificate_generator import CertificateGenerator
            CertificateGenerator(self.root, self.db_manager, self.settings)
        except ImportError:
            show_error_message("خطأ", "نافذة الشهادات غير متوفرة")
        except Exception as e:
            show_error_message("خطأ", f"خطأ في فتح نافذة الشهادات: {str(e)}")
        finally:
            self.update_status("جاهز")
    
    def generate_seat_cards(self):
        """إنتاج بطاقات الجلوس"""
        self.update_status("فتح نافذة بطاقات الجلوس...")
        try:
            from gui.seat_cards_generator import SeatCardsGenerator
            SeatCardsGenerator(self.root, self.db_manager, self.settings)
        except ImportError:
            show_error_message("خطأ", "نافذة بطاقات الجلوس غير متوفرة")
        except Exception as e:
            show_error_message("خطأ", f"خطأ في فتح نافذة بطاقات الجلوس: {str(e)}")
        finally:
            self.update_status("جاهز")
    
    def open_settings(self):
        """فتح نافذة الإعدادات"""
        self.update_status("فتح نافذة الإعدادات...")
        try:
            from gui.settings_window import SettingsWindow
            settings_window = SettingsWindow(self.root, self.settings)
            # تحديث الإحصائيات بعد إغلاق نافذة الإعدادات
            self.root.wait_window(settings_window.window)
            self.update_statistics()
        except ImportError:
            show_error_message("خطأ", "نافذة الإعدادات غير متوفرة")
        except Exception as e:
            show_error_message("خطأ", f"خطأ في فتح نافذة الإعدادات: {str(e)}")
        finally:
            self.update_status("جاهز")
