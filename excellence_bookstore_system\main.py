#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام محاسبة مكتبة التميز
Excellence Bookstore Accounting System

المنظومة الإلكترونية - إعداد محمد مطرود
Electronic System - Prepared by <PERSON>

نظام محاسبة متكامل لإدارة المبيعات والمصاريف والديون
مصمم خصيصاً لمكتبة التميز مع دعم كامل للغة العربية
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from datetime import datetime
import platform

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from database.db_manager import DatabaseManager
    from gui.main_window import MainWindow
    from utils.arabic_support import setup_arabic_font
    from utils.config import AppConfig
except ImportError as e:
    print(f"خطأ في استيراد المكتبات: {e}")
    print("تأكد من وجود جميع ملفات النظام")
    sys.exit(1)

class ExcellenceBookstoreSystem:
    """
    النظام الرئيسي لمحاسبة مكتبة التميز
    Main Excellence Bookstore Accounting System
    """
    
    def __init__(self):
        """تهيئة النظام"""
        self.root = tk.Tk()
        self.db_manager = None
        self.main_window = None
        self.config = AppConfig()
        
        # إعداد النظام
        self.setup_system()
    
    def setup_system(self):
        """إعداد النظام الرئيسي"""
        try:
            print("بدء تهيئة نظام محاسبة مكتبة التميز...")
            
            # إعداد النافذة الرئيسية
            self.setup_main_window()
            
            # إعداد الخط العربي
            setup_arabic_font(self.root)
            
            # إعداد قاعدة البيانات
            self.setup_database()
            
            # إنشاء النافذة الرئيسية
            self.main_window = MainWindow(self.root, self.db_manager, self.config)
            
            # ربط إغلاق النافذة
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            
            print("تم إعداد النظام بنجاح")
            
        except Exception as e:
            error_msg = f"خطأ في إعداد النظام: {str(e)}"
            print(error_msg)
            messagebox.showerror("خطأ في التهيئة", error_msg)
            sys.exit(1)
    
    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        # عنوان النافذة
        self.root.title("نظام محاسبة مكتبة التميز - المنظومة الإلكترونية")
        
        # حجم النافذة
        window_width = 1400
        window_height = 900
        self.root.geometry(f"{window_width}x{window_height}")
        
        # الحد الأدنى لحجم النافذة
        self.root.minsize(1200, 800)
        
        # لون الخلفية
        self.root.configure(bg='#f8f9fa')
        
        # توسيط النافذة
        self.center_window(window_width, window_height)
        
        # إعداد أيقونة التطبيق
        self.setup_app_icon()
        
        # تعيين حالة النافذة
        self.root.state('zoomed')  # فتح النافذة بحجم كامل على Windows
    
    def center_window(self, width, height):
        """توسيط النافذة على الشاشة"""
        # الحصول على أبعاد الشاشة
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # حساب موقع النافذة
        x = (screen_width // 2) - (width // 2)
        y = (screen_height // 2) - (height // 2)
        
        # تطبيق الموقع
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def setup_app_icon(self):
        """إعداد أيقونة التطبيق"""
        try:
            icon_path = os.path.join(current_dir, "resources", "icons", "app_icon.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except Exception as e:
            print(f"تعذر تحميل أيقونة التطبيق: {e}")
    
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            print("إعداد قاعدة البيانات...")
            
            # إنشاء مجلد البيانات إذا لم يكن موجوداً
            data_dir = os.path.join(current_dir, "data")
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
                print("تم إنشاء مجلد البيانات")
            
            # مسار قاعدة البيانات
            db_path = os.path.join(data_dir, "excellence_bookstore.db")
            
            # إنشاء مدير قاعدة البيانات
            self.db_manager = DatabaseManager(db_path)
            
            print("تم إعداد قاعدة البيانات بنجاح")
            
        except Exception as e:
            error_msg = f"خطأ في إعداد قاعدة البيانات: {str(e)}"
            print(error_msg)
            messagebox.showerror("خطأ في قاعدة البيانات", error_msg)
            raise
    
    def on_closing(self):
        """معالجة إغلاق التطبيق"""
        try:
            # حفظ الإعدادات
            self.config.save_config()
            
            # إغلاق قاعدة البيانات
            if self.db_manager:
                self.db_manager.close_connection()
            
            print("تم إغلاق النظام بنجاح")
            self.root.destroy()
            
        except Exception as e:
            print(f"خطأ في إغلاق النظام: {e}")
            self.root.destroy()
    
    def run(self):
        """تشغيل النظام"""
        try:
            print("=" * 70)
            print("نظام محاسبة مكتبة التميز")
            print("Excellence Bookstore Accounting System")
            print("المنظومة الإلكترونية - إعداد محمد مطرود")
            print("Electronic System - Prepared by Mohammed Matroud")
            print("=" * 70)
            print("بدء تشغيل النظام...")
            
            # تشغيل الحلقة الرئيسية
            self.root.mainloop()
            
        except Exception as e:
            error_msg = f"خطأ في تشغيل النظام: {str(e)}"
            print(error_msg)
            messagebox.showerror("خطأ في التشغيل", error_msg)
        finally:
            print("تم إنهاء النظام")

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 6):
        print("خطأ: يتطلب هذا النظام Python 3.6 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        input("اضغط Enter للخروج...")
        return False
    return True

def check_dependencies():
    """التحقق من المكتبات المطلوبة"""
    required_modules = ['tkinter', 'sqlite3', 'datetime', 'platform', 'os', 'sys']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"خطأ: المكتبات التالية غير متوفرة: {', '.join(missing_modules)}")
        input("اضغط Enter للخروج...")
        return False
    
    return True

def show_welcome_message():
    """عرض رسالة الترحيب"""
    print("\n" + "=" * 70)
    print("🏪 مرحباً بك في نظام محاسبة مكتبة التميز")
    print("📊 نظام متكامل لإدارة المبيعات والمصاريف والديون")
    print("🔧 المنظومة الإلكترونية - إعداد محمد مطرود")
    print("=" * 70)

def main():
    """الدالة الرئيسية"""
    try:
        # عرض رسالة الترحيب
        show_welcome_message()
        
        # التحقق من المتطلبات
        if not check_python_version():
            return
        
        if not check_dependencies():
            return
        
        # إنشاء وتشغيل النظام
        system = ExcellenceBookstoreSystem()
        system.run()
        
    except KeyboardInterrupt:
        print("\nتم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        error_msg = f"خطأ عام في النظام: {str(e)}"
        print(error_msg)
        try:
            messagebox.showerror("خطأ", error_msg)
        except:
            pass

if __name__ == "__main__":
    main()
