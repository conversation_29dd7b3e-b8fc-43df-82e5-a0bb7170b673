# نظام إدارة الطلاب - الإصدار المحسن 2.0
## معهد المتوسط للمهن الشاملة - اجخرة

نظام شامل ومحسن لإدارة بيانات الطلاب والدرجات وإنتاج الشهادات والتقارير مع واجهة عربية متطورة.

## ✨ الميزات الجديدة في الإصدار 2.0

- 🎨 **واجهة محسنة**: تصميم عصري وسهل الاستخدام
- 🔍 **بحث متقدم**: بحث سريع ومتقدم مع فلترة
- 📊 **إحصائيات تفاعلية**: لوحة إحصائيات محدثة تلقائياً
- 💾 **نسخ احتياطي تلقائي**: حماية البيانات مع نسخ احتياطي منتظم
- ⚙️ **إعدادات متقدمة**: تخصيص شامل للنظام
- 🔐 **أمان محسن**: تشفير البيانات وسجل العمليات
- 📱 **تجربة مستخدم محسنة**: تلميحات وإرشادات تفاعلية
- 🌐 **دعم عربي كامل**: خطوط وتخطيط محسن للغة العربية

## المتطلبات

### الحد الأدنى:
- **Python**: 3.6 أو أحدث
- **نظام التشغيل**: Windows 7+, macOS 10.12+, Linux
- **الذاكرة**: 512 MB RAM
- **مساحة القرص**: 100 MB

### المستحسن:
- **Python**: 3.9 أو أحدث
- **نظام التشغيل**: Windows 10/11
- **الذاكرة**: 2 GB RAM أو أكثر
- **مساحة القرص**: 500 MB

## 🚀 طرق التشغيل

### الطريقة الأولى (الأسهل):
```bash
# انقر نقراً مزدوجاً على
run_app.bat
```

### الطريقة الثانية:
```bash
python main.py
```

### الطريقة الثالثة (اختبار المكونات):
```bash
python simple_test.py
```

## 📋 الوظائف الرئيسية

### 👥 إدارة الطلاب المتقدمة
- ✅ إضافة طلاب جدد مع التحقق من البيانات
- ✅ تعديل وحذف بيانات الطلاب
- ✅ توليد أرقام الجلوس الذكي
- ✅ إدارة معلومات أولياء الأمور
- ✅ تتبع حالة الطلاب (نشط، منقطع، متخرج)
- ✅ إضافة ملاحظات وتعليقات

### 📝 نظام الدرجات المحسن
- ✅ إدخال درجات متعدد المستويات
- ✅ حساب تلقائي للدرجات النهائية
- ✅ إدارة درجات الدور الثاني
- ✅ تقييم الأداء الأكاديمي
- ✅ تقارير درجات مفصلة
- ✅ إحصائيات النجاح والرسوب

### 🔍 البحث والاستعلام المتطور
- ✅ بحث سريع متعدد المعايير
- ✅ فلترة متقدمة حسب الشعبة والحالة
- ✅ عرض تفصيلي لبيانات الطلاب
- ✅ تصدير نتائج البحث
- ✅ حفظ عمليات البحث المتكررة

### 📜 إنتاج الشهادات والتقارير
- ✅ شهادات نجاح مخصصة
- ✅ كشوف درجات شاملة
- ✅ تقارير إحصائية متنوعة
- ✅ معاينة قبل الطباعة
- ✅ تصدير بصيغ متعددة (PDF, Excel)
- ✅ قوالب قابلة للتخصيص

### 🎫 بطاقات الجلوس الذكية
- ✅ إنتاج بطاقات جلوس احترافية
- ✅ تخصيص معلومات الامتحان
- ✅ طباعة مجمعة وفردية
- ✅ باركود وQR Code للتحقق

### ⚙️ إعدادات النظام الشاملة
- ✅ إعدادات المعهد والمؤسسة
- ✅ إدارة السنوات الدراسية
- ✅ تخصيص درجات النجاح
- ✅ إدارة الشعب والمواد
- ✅ إعدادات النسخ الاحتياطي
- ✅ تخصيص واجهة المستخدم

## 💻 إنشاء ملف تنفيذي (.exe)

لإنشاء ملف تنفيذي مستقل لا يحتاج Python:

### 1. تثبيت PyInstaller:
```bash
pip install pyinstaller
```

### 2. بناء الملف التنفيذي:
```bash
python build_exe.py
```

### 3. الملفات المُنشأة:
- `dist/StudentManagementSystem.exe` - الملف التنفيذي
- `dist/تشغيل_النظام.bat` - ملف تشغيل سهل
- `dist/installer.bat` - سكريبت تثبيت على النظام

## 📁 هيكل المشروع المحسن

```
student_management_app/
├── 📄 main.py                    # الملف الرئيسي المحسن
├── 📄 simple_test.py             # اختبار المكونات
├── 📄 build_exe.py               # بناء الملف التنفيذي
├── 📄 run_app.bat                # ملف تشغيل Windows
├── 📄 requirements.txt           # متطلبات Python
├── 📄 setup.py                   # إعداد التطبيق
├── 📄 README.md                  # دليل المشروع
│
├── 📂 config/                    # إعدادات التطبيق
│   ├── __init__.py
│   └── settings.py               # إدارة الإعدادات المتقدمة
│
├── 📂 database/                  # قاعدة البيانات المحسنة
│   ├── __init__.py
│   └── db_manager.py             # مدير قاعدة البيانات المتطور
│
├── 📂 gui/                       # واجهة المستخدم المحسنة
│   ├── __init__.py
│   ├── main_window.py            # النافذة الرئيسية المطورة
│   ├── student_entry.py          # إدارة الطلاب المحسنة
│   ├── grade_entry.py            # إدارة الدرجات (قريباً)
│   ├── student_search.py         # البحث المتقدم (قريباً)
│   ├── certificate_generator.py # مولد الشهادات (قريباً)
│   ├── seat_cards_generator.py   # مولد بطاقات الجلوس (قريباً)
│   └── settings_window.py        # نافذة الإعدادات (قريباً)
│
├── 📂 utils/                     # أدوات مساعدة محسنة
│   ├── __init__.py
│   ├── arabic_support.py         # دعم عربي متطور
│   ├── validators.py             # التحقق من البيانات
│   └── helpers.py                # دوال مساعدة شاملة
│
├── 📂 resources/                 # موارد التطبيق
│   ├── icons/                    # أيقونات التطبيق
│   ├── templates/                # قوالب الشهادات
│   └── fonts/                    # خطوط عربية
│
├── 📂 data/                      # بيانات التطبيق
│   ├── student_data.db           # قاعدة البيانات الرئيسية
│   ├── app_settings.json         # إعدادات التطبيق
│   └── backups/                  # النسخ الاحتياطية
│
├── 📂 tests/                     # اختبارات التطبيق
│   └── test_*.py                 # ملفات الاختبار
│
└── 📂 docs/                      # الوثائق
    └── user_guide.md             # دليل المستخدم الشامل
```

## قاعدة البيانات

يستخدم النظام قاعدة بيانات SQLite مع الجداول التالية:

- **students**: بيانات الطلاب
- **sections**: الشعب الدراسية
- **subjects**: المواد الدراسية
- **grades**: درجات الطلاب

## الاستخدام

### إضافة طالب جديد:
1. اضغط على "إدخال بيانات الطلاب"
2. املأ البيانات المطلوبة
3. اختر الشعبة
4. اضغط "حفظ"

### إدخال الدرجات:
1. اضغط على "إدخال الدرجات"
2. اختر الشعبة والمادة
3. اضغط "تحميل الطلاب"
4. أدخل الدرجات
5. اضغط "حفظ الدرجات"

### البحث عن طالب:
1. اضغط على "البحث عن طالب"
2. أدخل الاسم أو الرقم الوطني أو رقم الجلوس
3. اضغط "بحث"
4. انقر نقراً مزدوجاً لعرض الدرجات

### إنتاج الشهادات:
1. اضغط على "إنتاج الشهادات"
2. اختر الشعبة والسنة الدراسية
3. حدد الطلاب المطلوبين
4. اضغط "إنتاج الشهادات"

## النسخ الاحتياطي

يُنصح بعمل نسخة احتياطية من مجلد `data` بانتظام للحفاظ على بيانات الطلاب.

## الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع إدارة النظام.

## الترخيص

هذا البرنامج مطور خصيصاً لمعهد المتوسط للمهن الشاملة - اجخرة.
