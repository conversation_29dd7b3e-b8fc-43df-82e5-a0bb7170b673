#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للتطبيق
Quick Application Test
"""

import tkinter as tk
import sys
import os

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from main import StudentManagementApp
    
    def test_app():
        """اختبار التطبيق"""
        print("بدء اختبار التطبيق...")
        
        try:
            app = StudentManagementApp()
            print("تم إنشاء التطبيق بنجاح")
            
            # تشغيل التطبيق
            app.run()
            
        except Exception as e:
            print(f"خطأ في تشغيل التطبيق: {e}")
            import traceback
            traceback.print_exc()
    
    if __name__ == "__main__":
        test_app()
        
except ImportError as e:
    print(f"خطأ في استيراد التطبيق: {e}")
    print("تأكد من وجود جميع الملفات المطلوبة")
except Exception as e:
    print(f"خطأ عام: {e}")
    import traceback
    traceback.print_exc()
