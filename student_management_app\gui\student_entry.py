#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدخال بيانات الطلاب المحسنة
Enhanced Student Entry Window

معهد المتوسط للمهن الشاملة - اجخرة
الإصدار المحسن 2.0
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import sys
import os

# إضافة مسار المكتبات
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, current_dir)

from utils.arabic_support import *
from utils.validators import DataValidator
from utils.helpers import *

class StudentEntryWindow:
    """نافذة إدخال بيانات الطلاب المحسنة"""
    
    def __init__(self, parent, db_manager, settings, edit_mode=False, student_data=None):
        """تهيئة النافذة"""
        self.parent = parent
        self.db_manager = db_manager
        self.settings = settings
        self.edit_mode = edit_mode
        self.student_data = student_data
        self.current_student_id = None
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.setup_ui()
        
        # تحميل البيانات في وضع التحرير
        if self.edit_mode and self.student_data:
            self.load_student_data()
        
        # تحميل قائمة الطلاب
        self.refresh_students_list()
    
    def setup_window(self):
        """إعداد النافذة"""
        title = "تحرير بيانات الطالب" if self.edit_mode else "إدارة الطلاب"
        self.window.title(title)
        self.window.geometry("1200x800")
        self.window.configure(bg='#ecf0f1')
        self.window.resizable(True, True)
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # توسيط النافذة
        center_window(self.window, 1200, 800)
        
        # إعداد أيقونة النافذة
        setup_window_icon(self.window)
        
        # ربط إغلاق النافذة
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # رأس النافذة
        self.create_header()
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.window, bg='#ecf0f1')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # تقسيم النافذة إلى قسمين
        left_frame = tk.Frame(main_frame, bg='#ecf0f1')
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        right_frame = tk.Frame(main_frame, bg='#ecf0f1')
        right_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        # إنشاء نموذج الإدخال
        self.create_form(left_frame)
        
        # إنشاء قائمة الطلاب
        self.create_students_list(right_frame)
    
    def create_header(self):
        """إنشاء رأس النافذة"""
        header_frame = tk.Frame(self.window, bg='#3498db', height=80)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        # العنوان الرئيسي
        title_text = "تحرير بيانات الطالب" if self.edit_mode else "إدارة الطلاب"
        title_label = tk.Label(
            header_frame,
            text=title_text,
            font=get_arabic_font(size=20, weight='bold'),
            bg='#3498db',
            fg='white'
        )
        title_label.pack(pady=20)
    
    def create_form(self, parent):
        """إنشاء نموذج إدخال البيانات"""
        form_frame = tk.LabelFrame(
            parent,
            text="بيانات الطالب",
            font=get_arabic_font(size=14, weight='bold'),
            bg='#ecf0f1',
            fg='#2c3e50',
            padx=15,
            pady=15
        )
        form_frame.pack(fill='both', expand=True)
        
        # إنشاء متغيرات النموذج
        self.create_form_variables()
        
        # إنشاء حقول الإدخال
        self.create_form_fields(form_frame)
        
        # إنشاء أزرار التحكم
        self.create_form_buttons(form_frame)
    
    def create_form_variables(self):
        """إنشاء متغيرات النموذج"""
        self.full_name_var = tk.StringVar()
        self.national_id_var = tk.StringVar()
        self.section_var = tk.StringVar()
        self.birth_date_var = tk.StringVar()
        self.gender_var = tk.StringVar()
        self.phone_var = tk.StringVar()
        self.address_var = tk.StringVar()
        self.guardian_name_var = tk.StringVar()
        self.guardian_phone_var = tk.StringVar()
        self.notes_var = tk.StringVar()
        
        # تعيين القيم الافتراضية
        self.gender_var.set("ذكر")
    
    def create_form_fields(self, parent):
        """إنشاء حقول الإدخال"""
        # إطار الحقول
        fields_frame = tk.Frame(parent, bg='#ecf0f1')
        fields_frame.pack(fill='both', expand=True, pady=(0, 15))
        
        # تكوين الشبكة
        fields_frame.columnconfigure(1, weight=1)
        fields_frame.columnconfigure(3, weight=1)
        
        row = 0
        
        # الاسم الكامل (مطلوب)
        self.create_field(
            fields_frame, "الاسم الكامل *", self.full_name_var,
            row, 0, required=True, width=25
        )
        
        # الرقم الوطني (مطلوب)
        self.create_field(
            fields_frame, "الرقم الوطني *", self.national_id_var,
            row, 2, required=True, width=25
        )
        
        row += 1
        
        # الشعبة (مطلوب)
        self.create_section_field(fields_frame, row, 0)
        
        # الجنس (مطلوب)
        self.create_gender_field(fields_frame, row, 2)
        
        row += 1
        
        # تاريخ الميلاد
        self.create_field(
            fields_frame, "تاريخ الميلاد", self.birth_date_var,
            row, 0, placeholder="YYYY-MM-DD", width=25
        )
        
        # رقم الهاتف
        self.create_field(
            fields_frame, "رقم الهاتف", self.phone_var,
            row, 2, width=25
        )
        
        row += 1
        
        # العنوان
        self.create_field(
            fields_frame, "العنوان", self.address_var,
            row, 0, columnspan=4, width=55
        )
        
        row += 1
        
        # اسم ولي الأمر
        self.create_field(
            fields_frame, "اسم ولي الأمر", self.guardian_name_var,
            row, 0, width=25
        )
        
        # رقم هاتف ولي الأمر
        self.create_field(
            fields_frame, "رقم هاتف ولي الأمر", self.guardian_phone_var,
            row, 2, width=25
        )
        
        row += 1
        
        # الملاحظات
        self.create_notes_field(fields_frame, row, 0)
    
    def create_field(self, parent, label_text, variable, row, column, 
                    required=False, placeholder="", columnspan=2, width=20):
        """إنشاء حقل إدخال"""
        # التسمية
        label_text_display = f"{label_text} *" if required else label_text
        label = create_arabic_label(
            parent,
            label_text_display,
            fg='#e74c3c' if required else '#2c3e50'
        )
        label.grid(row=row, column=column, sticky='e', padx=(0, 10), pady=5)
        
        # حقل الإدخال
        entry = RTLEntry(
            parent,
            textvariable=variable,
            width=width,
            font=get_arabic_font(size=11)
        )
        entry.grid(
            row=row, column=column+1, 
            columnspan=columnspan-1,
            sticky='ew', padx=(0, 20), pady=5
        )
        
        # إضافة نص توضيحي
        if placeholder:
            create_tooltip(entry, placeholder)
        
        # التحقق من البيانات عند فقدان التركيز
        if required:
            entry.bind('<FocusOut>', lambda e: self.validate_field(variable, label_text))
        
        return entry
    
    def create_section_field(self, parent, row, column):
        """إنشاء حقل اختيار الشعبة"""
        # التسمية
        label = create_arabic_label(parent, "الشعبة *", fg='#e74c3c')
        label.grid(row=row, column=column, sticky='e', padx=(0, 10), pady=5)
        
        # قائمة منسدلة للشعب
        self.section_combo = ttk.Combobox(
            parent,
            textvariable=self.section_var,
            font=get_arabic_font(size=11),
            width=22,
            state='readonly'
        )
        self.section_combo.grid(
            row=row, column=column+1,
            sticky='ew', padx=(0, 20), pady=5
        )
        
        # تحميل الشعب
        self.load_sections()
    
    def create_gender_field(self, parent, row, column):
        """إنشاء حقل اختيار الجنس"""
        # التسمية
        label = create_arabic_label(parent, "الجنس *", fg='#e74c3c')
        label.grid(row=row, column=column, sticky='e', padx=(0, 10), pady=5)
        
        # إطار أزرار الاختيار
        gender_frame = tk.Frame(parent, bg='#ecf0f1')
        gender_frame.grid(
            row=row, column=column+1,
            sticky='w', padx=(0, 20), pady=5
        )
        
        # أزرار الاختيار
        male_radio = tk.Radiobutton(
            gender_frame,
            text="ذكر",
            variable=self.gender_var,
            value="ذكر",
            font=get_arabic_font(size=11),
            bg='#ecf0f1'
        )
        male_radio.pack(side='right', padx=10)
        
        female_radio = tk.Radiobutton(
            gender_frame,
            text="أنثى",
            variable=self.gender_var,
            value="أنثى",
            font=get_arabic_font(size=11),
            bg='#ecf0f1'
        )
        female_radio.pack(side='right', padx=10)
    
    def create_notes_field(self, parent, row, column):
        """إنشاء حقل الملاحظات"""
        # التسمية
        label = create_arabic_label(parent, "ملاحظات")
        label.grid(row=row, column=column, sticky='ne', padx=(0, 10), pady=5)
        
        # مربع النص
        self.notes_text = RTLText(
            parent,
            height=4,
            width=50,
            font=get_arabic_font(size=11),
            wrap='word'
        )
        self.notes_text.grid(
            row=row, column=column+1,
            columnspan=3,
            sticky='ew', padx=(0, 20), pady=5
        )
        
        # شريط التمرير
        notes_scrollbar = ttk.Scrollbar(parent, orient='vertical', command=self.notes_text.yview)
        notes_scrollbar.grid(row=row, column=column+4, sticky='ns', pady=5)
        self.notes_text.configure(yscrollcommand=notes_scrollbar.set)
    
    def create_form_buttons(self, parent):
        """إنشاء أزرار النموذج"""
        buttons_frame = tk.Frame(parent, bg='#ecf0f1')
        buttons_frame.pack(fill='x', pady=(15, 0))
        
        # زر الحفظ
        save_text = "تحديث البيانات" if self.edit_mode else "حفظ الطالب"
        save_btn = create_styled_button(
            buttons_frame,
            save_text,
            "💾",
            command=self.save_student,
            bg='#27ae60',
            fg='white',
            width=15
        )
        save_btn.pack(side='right', padx=5)
        
        # زر المسح
        clear_btn = create_styled_button(
            buttons_frame,
            "مسح النموذج",
            "🗑️",
            command=self.clear_form,
            bg='#f39c12',
            fg='white',
            width=15
        )
        clear_btn.pack(side='right', padx=5)
        
        # زر الإلغاء
        cancel_btn = create_styled_button(
            buttons_frame,
            "إلغاء",
            "❌",
            command=self.on_closing,
            bg='#e74c3c',
            fg='white',
            width=15
        )
        cancel_btn.pack(side='right', padx=5)
        
        # زر طالب جديد (في وضع التحرير)
        if self.edit_mode:
            new_btn = create_styled_button(
                buttons_frame,
                "طالب جديد",
                "👤",
                command=self.new_student,
                bg='#3498db',
                fg='white',
                width=15
            )
            new_btn.pack(side='left', padx=5)
    
    def create_students_list(self, parent):
        """إنشاء قائمة الطلاب"""
        list_frame = tk.LabelFrame(
            parent,
            text="قائمة الطلاب",
            font=get_arabic_font(size=14, weight='bold'),
            bg='#ecf0f1',
            fg='#2c3e50',
            padx=10,
            pady=10
        )
        list_frame.pack(fill='both', expand=True)
        
        # إطار البحث
        search_frame = tk.Frame(list_frame, bg='#ecf0f1')
        search_frame.pack(fill='x', pady=(0, 10))
        
        # حقل البحث
        search_label = create_arabic_label(search_frame, "بحث:")
        search_label.pack(side='right', padx=(0, 10))
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        
        search_entry = RTLEntry(
            search_frame,
            textvariable=self.search_var,
            width=30,
            font=get_arabic_font(size=11)
        )
        search_entry.pack(side='right', padx=(0, 10))
        
        # قائمة الطلاب
        list_container = tk.Frame(list_frame, bg='#ecf0f1')
        list_container.pack(fill='both', expand=True)
        
        # إنشاء Treeview
        columns = ('name', 'national_id', 'seat_number', 'section')
        self.students_tree = ttk.Treeview(
            list_container,
            columns=columns,
            show='headings',
            height=15
        )
        
        # تعيين عناوين الأعمدة
        self.students_tree.heading('name', text='الاسم')
        self.students_tree.heading('national_id', text='الرقم الوطني')
        self.students_tree.heading('seat_number', text='رقم الجلوس')
        self.students_tree.heading('section', text='الشعبة')
        
        # تعيين عرض الأعمدة
        self.students_tree.column('name', width=200)
        self.students_tree.column('national_id', width=120)
        self.students_tree.column('seat_number', width=100)
        self.students_tree.column('section', width=120)
        
        # شريط التمرير
        tree_scrollbar = ttk.Scrollbar(
            list_container,
            orient='vertical',
            command=self.students_tree.yview
        )
        self.students_tree.configure(yscrollcommand=tree_scrollbar.set)
        
        # تخطيط العناصر
        self.students_tree.pack(side='left', fill='both', expand=True)
        tree_scrollbar.pack(side='right', fill='y')
        
        # ربط الأحداث
        self.students_tree.bind('<Double-1>', self.on_student_select)
        self.students_tree.bind('<Button-3>', self.show_context_menu)
        
        # إنشاء قائمة السياق
        self.create_context_menu()
    
    def create_context_menu(self):
        """إنشاء قائمة السياق للطلاب"""
        self.context_menu = tk.Menu(self.window, tearoff=0)
        self.context_menu.add_command(
            label="تحرير",
            command=self.edit_selected_student
        )
        self.context_menu.add_command(
            label="حذف",
            command=self.delete_selected_student
        )
        self.context_menu.add_separator()
        self.context_menu.add_command(
            label="عرض الدرجات",
            command=self.view_student_grades
        )
    
    def load_sections(self):
        """تحميل قائمة الشعب"""
        try:
            sections = self.db_manager.get_all_sections()
            self.section_combo['values'] = sections
            
            if sections and not self.section_var.get():
                self.section_var.set(sections[0])
                
        except Exception as e:
            show_error_message("خطأ", f"خطأ في تحميل الشعب: {str(e)}")
    
    def validate_field(self, variable, field_name):
        """التحقق من صحة حقل معين"""
        value = variable.get()
        
        if field_name == "الاسم الكامل":
            is_valid, message = DataValidator.validate_name(value)
        elif field_name == "الرقم الوطني":
            is_valid, message = DataValidator.validate_national_id(value)
        else:
            return True
        
        if not is_valid:
            show_warning_message("تحذير", f"{field_name}: {message}")
            return False
        
        return True

    def save_student(self):
        """حفظ بيانات الطالب"""
        try:
            # جمع البيانات من النموذج
            student_data = self.get_form_data()

            # التحقق من صحة البيانات
            is_valid, errors = DataValidator.validate_student_data(student_data)
            if not is_valid:
                error_message = "يرجى تصحيح الأخطاء التالية:\n\n" + "\n".join(errors)
                show_error_message("خطأ في البيانات", error_message)
                return

            if self.edit_mode and self.current_student_id:
                # تحديث الطالب الموجود
                self.db_manager.update_student(self.current_student_id, student_data)
                show_success_message("نجح التحديث", "تم تحديث بيانات الطالب بنجاح")
            else:
                # إضافة طالب جديد
                student_id, seat_number = self.db_manager.add_student(student_data)
                show_success_message(
                    "نجح الحفظ",
                    f"تم حفظ بيانات الطالب بنجاح\nرقم الجلوس: {seat_number}"
                )

                # مسح النموذج بعد الحفظ
                self.clear_form()

            # تحديث قائمة الطلاب
            self.refresh_students_list()

        except Exception as e:
            show_error_message("خطأ", f"خطأ في حفظ البيانات: {str(e)}")

    def get_form_data(self):
        """جمع البيانات من النموذج"""
        # الحصول على نص الملاحظات
        notes_text = self.notes_text.get("1.0", tk.END).strip()

        return {
            'full_name': clean_text(self.full_name_var.get()),
            'national_id': clean_text(self.national_id_var.get()),
            'section': self.section_var.get(),
            'birth_date': clean_text(self.birth_date_var.get()),
            'gender': self.gender_var.get(),
            'phone': clean_text(self.phone_var.get()),
            'address': clean_text(self.address_var.get()),
            'guardian_name': clean_text(self.guardian_name_var.get()),
            'guardian_phone': clean_text(self.guardian_phone_var.get()),
            'notes': notes_text
        }

    def clear_form(self):
        """مسح النموذج"""
        self.full_name_var.set('')
        self.national_id_var.set('')
        self.birth_date_var.set('')
        self.gender_var.set('ذكر')
        self.phone_var.set('')
        self.address_var.set('')
        self.guardian_name_var.set('')
        self.guardian_phone_var.set('')
        self.notes_text.delete("1.0", tk.END)

        # إعادة تعيين وضع التحرير
        self.edit_mode = False
        self.current_student_id = None

    def new_student(self):
        """إنشاء طالب جديد"""
        self.clear_form()
        self.edit_mode = False
        self.current_student_id = None

    def refresh_students_list(self):
        """تحديث قائمة الطلاب"""
        try:
            # مسح القائمة الحالية
            for item in self.students_tree.get_children():
                self.students_tree.delete(item)

            # الحصول على جميع الطلاب
            all_students = []
            sections = self.db_manager.get_all_sections()

            for section in sections:
                students = self.db_manager.get_students_by_section(section)
                all_students.extend(students)

            # إضافة الطلاب إلى القائمة
            for student in all_students:
                self.students_tree.insert('', 'end', values=(
                    student[1],  # full_name
                    student[2],  # national_id
                    student[3],  # seat_number
                    student[4]   # section
                ), tags=(student[0],))  # student_id في tags

        except Exception as e:
            show_error_message("خطأ", f"خطأ في تحديث قائمة الطلاب: {str(e)}")

    def on_search_change(self, *args):
        """البحث في قائمة الطلاب"""
        search_term = self.search_var.get().strip()

        if not search_term:
            self.refresh_students_list()
            return

        try:
            # البحث في قاعدة البيانات
            results = self.db_manager.search_student(search_term)

            # مسح القائمة الحالية
            for item in self.students_tree.get_children():
                self.students_tree.delete(item)

            # إضافة نتائج البحث
            for student in results:
                self.students_tree.insert('', 'end', values=(
                    student[1],  # full_name
                    student[2],  # national_id
                    student[3],  # seat_number
                    student[4]   # section
                ), tags=(student[0],))  # student_id في tags

        except Exception as e:
            print(f"خطأ في البحث: {e}")

    def on_student_select(self, event):
        """عند اختيار طالب من القائمة"""
        selection = self.students_tree.selection()
        if not selection:
            return

        # الحصول على معرف الطالب
        item = self.students_tree.item(selection[0])
        student_id = item['tags'][0] if item['tags'] else None

        if student_id:
            self.load_student_for_edit(student_id)

    def load_student_for_edit(self, student_id):
        """تحميل بيانات الطالب للتحرير"""
        try:
            student_data = self.db_manager.get_student_by_id(student_id)
            if not student_data:
                show_error_message("خطأ", "لم يتم العثور على بيانات الطالب")
                return

            # تحميل البيانات في النموذج
            self.full_name_var.set(student_data[1] or '')
            self.national_id_var.set(student_data[2] or '')
            self.section_var.set(student_data[4] or '')
            self.birth_date_var.set(student_data[5] or '')
            self.gender_var.set(student_data[6] or 'ذكر')
            self.phone_var.set(student_data[7] or '')
            self.address_var.set(student_data[8] or '')
            self.guardian_name_var.set(student_data[9] or '')
            self.guardian_phone_var.set(student_data[10] or '')

            # تحميل الملاحظات
            self.notes_text.delete("1.0", tk.END)
            if len(student_data) > 13 and student_data[13]:  # notes field
                self.notes_text.insert("1.0", student_data[13])

            # تعيين وضع التحرير
            self.edit_mode = True
            self.current_student_id = student_id

        except Exception as e:
            show_error_message("خطأ", f"خطأ في تحميل بيانات الطالب: {str(e)}")

    def show_context_menu(self, event):
        """عرض قائمة السياق"""
        # التحقق من وجود عنصر محدد
        item = self.students_tree.identify_row(event.y)
        if item:
            self.students_tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)

    def edit_selected_student(self):
        """تحرير الطالب المحدد"""
        selection = self.students_tree.selection()
        if not selection:
            show_warning_message("تحذير", "يرجى اختيار طالب للتحرير")
            return

        # الحصول على معرف الطالب
        item = self.students_tree.item(selection[0])
        student_id = item['tags'][0] if item['tags'] else None

        if student_id:
            self.load_student_for_edit(student_id)

    def delete_selected_student(self):
        """حذف الطالب المحدد"""
        selection = self.students_tree.selection()
        if not selection:
            show_warning_message("تحذير", "يرجى اختيار طالب للحذف")
            return

        # تأكيد الحذف
        if not ask_yes_no("تأكيد الحذف", "هل أنت متأكد من حذف هذا الطالب؟\nسيتم حذف جميع درجاته أيضاً"):
            return

        try:
            # الحصول على معرف الطالب
            item = self.students_tree.item(selection[0])
            student_id = item['tags'][0] if item['tags'] else None

            if student_id:
                # حذف الطالب
                self.db_manager.delete_student(student_id)
                show_success_message("نجح الحذف", "تم حذف الطالب بنجاح")

                # تحديث القائمة
                self.refresh_students_list()

                # مسح النموذج إذا كان الطالب المحذوف محملاً
                if self.current_student_id == student_id:
                    self.clear_form()

        except Exception as e:
            show_error_message("خطأ", f"خطأ في حذف الطالب: {str(e)}")

    def view_student_grades(self):
        """عرض درجات الطالب المحدد"""
        selection = self.students_tree.selection()
        if not selection:
            show_warning_message("تحذير", "يرجى اختيار طالب لعرض درجاته")
            return

        try:
            # الحصول على معرف الطالب
            item = self.students_tree.item(selection[0])
            student_id = item['tags'][0] if item['tags'] else None

            if student_id:
                # عرض رسالة مؤقتة
                show_info_message("عرض الدرجات", "سيتم إضافة نافذة عرض الدرجات قريباً")

        except Exception as e:
            show_error_message("خطأ", f"خطأ في عرض الدرجات: {str(e)}")

    def on_closing(self):
        """معالجة إغلاق النافذة"""
        # التحقق من وجود تغييرات غير محفوظة
        if self.has_unsaved_changes():
            if ask_yes_no("تأكيد الإغلاق", "هناك تغييرات غير محفوظة. هل تريد الإغلاق بدون حفظ؟"):
                self.window.destroy()
        else:
            self.window.destroy()

    def has_unsaved_changes(self):
        """التحقق من وجود تغييرات غير محفوظة"""
        # فحص بسيط للتحقق من وجود بيانات في النموذج
        return (
            self.full_name_var.get().strip() or
            self.national_id_var.get().strip() or
            self.phone_var.get().strip() or
            self.address_var.get().strip() or
            self.guardian_name_var.get().strip() or
            self.guardian_phone_var.get().strip() or
            self.notes_text.get("1.0", tk.END).strip()
        )
