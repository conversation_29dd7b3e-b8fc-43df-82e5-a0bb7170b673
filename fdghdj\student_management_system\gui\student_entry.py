#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدخال بيانات الطلاب
Student Entry Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import sys
import os

# إضافة مسار المكتبات
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.arabic_support import *

class StudentEntryWindow:
    def __init__(self, parent, db_manager, edit_mode=False, student_data=None):
        self.parent = parent
        self.db_manager = db_manager
        self.edit_mode = edit_mode
        self.student_data = student_data
        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.setup_ui()

        # إذا كان في وضع التحرير، تحميل البيانات
        if self.edit_mode and self.student_data:
            self.load_student_data()
        
    def setup_window(self):
        """إعداد النافذة"""
        title = "تحرير بيانات الطالب" if self.edit_mode else "إدخال بيانات الطلاب"
        self.window.title(title)
        self.window.geometry("800x600")
        self.window.configure(bg='#ecf0f1')
        self.window.resizable(True, True)

        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()

        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_frame = tk.Frame(self.window, bg='#3498db', height=60)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame,
            text="إدخال بيانات الطلاب",
            font=get_arabic_font(size=18, weight='bold'),
            bg='#3498db',
            fg='white'
        )
        title_label.pack(pady=15)
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.window, bg='#ecf0f1')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # إنشاء النموذج
        self.create_form(main_frame)
        
        # إنشاء قائمة الطلاب
        self.create_students_list(main_frame)
        
    def create_form(self, parent):
        """إنشاء نموذج إدخال البيانات"""
        form_frame = tk.LabelFrame(
            parent,
            text="بيانات الطالب",
            font=get_arabic_font(size=14, weight='bold'),
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        form_frame.pack(fill='x', pady=(0, 20))
        
        # متغيرات النموذج
        self.full_name_var = tk.StringVar()
        self.national_id_var = tk.StringVar()
        self.section_var = tk.StringVar()
        self.birth_date_var = tk.StringVar()
        self.gender_var = tk.StringVar()
        self.phone_var = tk.StringVar()
        
        # الصف الأول
        row1_frame = tk.Frame(form_frame, bg='#ecf0f1')
        row1_frame.pack(fill='x', padx=10, pady=10)
        
        # الاسم الكامل
        tk.Label(
            row1_frame,
            text="الاسم الكامل:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1',
            anchor='e'
        ).pack(side='right', padx=(0, 10))
        
        self.name_entry = RTLEntry(
            row1_frame,
            textvariable=self.full_name_var,
            font=get_arabic_font(size=12),
            width=30
        )
        self.name_entry.pack(side='right', padx=(0, 20))
        
        # الرقم الوطني
        tk.Label(
            row1_frame,
            text="الرقم الوطني:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1',
            anchor='e'
        ).pack(side='right', padx=(0, 10))
        
        self.national_id_entry = tk.Entry(
            row1_frame,
            textvariable=self.national_id_var,
            font=get_arabic_font(size=12),
            width=20
        )
        self.national_id_entry.pack(side='right')
        
        # الصف الثاني
        row2_frame = tk.Frame(form_frame, bg='#ecf0f1')
        row2_frame.pack(fill='x', padx=10, pady=10)
        
        # الشعبة
        tk.Label(
            row2_frame,
            text="الشعبة:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1',
            anchor='e'
        ).pack(side='right', padx=(0, 10))
        
        self.section_combo = ttk.Combobox(
            row2_frame,
            textvariable=self.section_var,
            font=get_arabic_font(size=12),
            width=25,
            state='readonly'
        )
        self.section_combo.pack(side='right', padx=(0, 20))
        
        # تحميل الشعب
        self.load_sections()
        
        # تاريخ الميلاد
        tk.Label(
            row2_frame,
            text="تاريخ الميلاد:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1',
            anchor='e'
        ).pack(side='right', padx=(0, 10))
        
        self.birth_date_entry = tk.Entry(
            row2_frame,
            textvariable=self.birth_date_var,
            font=get_arabic_font(size=12),
            width=15
        )
        self.birth_date_entry.pack(side='right')
        
        # إضافة تلميح للتاريخ
        tk.Label(
            row2_frame,
            text="(YYYY-MM-DD)",
            font=get_arabic_font(size=10),
            bg='#ecf0f1',
            fg='#7f8c8d'
        ).pack(side='right', padx=(5, 0))
        
        # الصف الثالث
        row3_frame = tk.Frame(form_frame, bg='#ecf0f1')
        row3_frame.pack(fill='x', padx=10, pady=10)
        
        # الجنس
        tk.Label(
            row3_frame,
            text="الجنس:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1',
            anchor='e'
        ).pack(side='right', padx=(0, 10))
        
        gender_frame = tk.Frame(row3_frame, bg='#ecf0f1')
        gender_frame.pack(side='right', padx=(0, 20))
        
        tk.Radiobutton(
            gender_frame,
            text="ذكر",
            variable=self.gender_var,
            value="ذكر",
            font=get_arabic_font(size=12),
            bg='#ecf0f1'
        ).pack(side='right', padx=(0, 10))
        
        tk.Radiobutton(
            gender_frame,
            text="أنثى",
            variable=self.gender_var,
            value="أنثى",
            font=get_arabic_font(size=12),
            bg='#ecf0f1'
        ).pack(side='right')
        
        # رقم الهاتف
        tk.Label(
            row3_frame,
            text="رقم الهاتف:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1',
            anchor='e'
        ).pack(side='right', padx=(0, 10))
        
        self.phone_entry = tk.Entry(
            row3_frame,
            textvariable=self.phone_var,
            font=get_arabic_font(size=12),
            width=20
        )
        self.phone_entry.pack(side='right')
        
        # أزرار التحكم
        buttons_frame = tk.Frame(form_frame, bg='#ecf0f1')
        buttons_frame.pack(fill='x', padx=10, pady=20)
        
        # زر الحفظ
        save_btn = create_arabic_button(
            buttons_frame,
            "حفظ الطالب",
            command=self.save_student,
            bg='#27ae60',
            fg='white',
            width=15
        )
        save_btn.pack(side='right', padx=(0, 10))
        
        # زر المسح
        clear_btn = create_arabic_button(
            buttons_frame,
            "مسح البيانات",
            command=self.clear_form,
            bg='#e74c3c',
            fg='white',
            width=15
        )
        clear_btn.pack(side='right', padx=(0, 10))
        
        # زر إضافة شعبة جديدة
        add_section_btn = create_arabic_button(
            buttons_frame,
            "إضافة شعبة",
            command=self.add_new_section,
            bg='#f39c12',
            fg='white',
            width=15
        )
        add_section_btn.pack(side='left')
        
    def create_students_list(self, parent):
        """إنشاء قائمة الطلاب"""
        list_frame = tk.LabelFrame(
            parent,
            text="قائمة الطلاب",
            font=get_arabic_font(size=14, weight='bold'),
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        list_frame.pack(fill='both', expand=True)
        
        # إطار التحكم
        control_frame = tk.Frame(list_frame, bg='#ecf0f1')
        control_frame.pack(fill='x', padx=10, pady=10)
        
        # اختيار الشعبة للعرض
        tk.Label(
            control_frame,
            text="عرض طلاب الشعبة:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1'
        ).pack(side='right', padx=(0, 10))
        
        self.display_section_var = tk.StringVar()
        self.display_section_combo = ttk.Combobox(
            control_frame,
            textvariable=self.display_section_var,
            font=get_arabic_font(size=12),
            width=20,
            state='readonly'
        )
        self.display_section_combo.pack(side='right', padx=(0, 10))
        self.display_section_combo.bind('<<ComboboxSelected>>', self.load_students)
        
        # زر التحديث
        refresh_btn = create_arabic_button(
            control_frame,
            "تحديث",
            command=self.refresh_students_list,
            bg='#3498db',
            fg='white',
            width=10
        )
        refresh_btn.pack(side='left')
        
        # جدول الطلاب
        columns = ('الاسم الكامل', 'الرقم الوطني', 'رقم الجلوس', 'تاريخ الميلاد', 'الجنس')
        
        self.students_tree = ttk.Treeview(
            list_frame,
            columns=columns,
            show='headings',
            height=10
        )
        
        # تكوين الأعمدة
        for col in columns:
            self.students_tree.heading(col, text=col, anchor='center')
            self.students_tree.column(col, width=150, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.students_tree.yview)
        self.students_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول وشريط التمرير
        self.students_tree.pack(side='left', fill='both', expand=True, padx=(10, 0), pady=(0, 10))
        scrollbar.pack(side='right', fill='y', padx=(0, 10), pady=(0, 10))
        
        # تحميل الشعب في قائمة العرض
        self.load_display_sections()
        
    def load_sections(self):
        """تحميل الشعب في القائمة المنسدلة"""
        try:
            sections = self.db_manager.get_all_sections()
            self.section_combo['values'] = sections
            if sections:
                self.section_combo.set(sections[0])
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الشعب: {str(e)}")
    
    def load_display_sections(self):
        """تحميل الشعب في قائمة العرض"""
        try:
            sections = self.db_manager.get_all_sections()
            self.display_section_combo['values'] = sections
            if sections:
                self.display_section_combo.set(sections[0])
                self.load_students()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الشعب: {str(e)}")
    
    def load_students(self, event=None):
        """تحميل طلاب الشعبة المحددة"""
        # مسح البيانات السابقة
        for item in self.students_tree.get_children():
            self.students_tree.delete(item)
        
        section = self.display_section_var.get()
        if not section:
            return
        
        try:
            students = self.db_manager.get_students_by_section(section)
            
            for student in students:
                # student = (id, full_name, national_id, seat_number, birth_date, gender, phone)
                self.students_tree.insert('', 'end', values=(
                    student[1],  # الاسم الكامل
                    student[2],  # الرقم الوطني
                    student[3],  # رقم الجلوس
                    student[4] if student[4] else '',  # تاريخ الميلاد
                    student[5] if student[5] else ''   # الجنس
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الطلاب: {str(e)}")
    
    def save_student(self):
        """حفظ بيانات الطالب"""
        # التحقق من البيانات المطلوبة
        if not self.full_name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال الاسم الكامل")
            return
        
        if not self.national_id_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال الرقم الوطني")
            return
        
        if not self.section_var.get():
            messagebox.showerror("خطأ", "يرجى اختيار الشعبة")
            return
        
        # التحقق من صحة الرقم الوطني
        national_id = self.national_id_var.get().strip()
        if not national_id.isdigit() or len(national_id) != 12:
            messagebox.showerror("خطأ", "الرقم الوطني يجب أن يكون 12 رقماً")
            return
        
        # التحقق من تاريخ الميلاد
        birth_date = self.birth_date_var.get().strip()
        if birth_date:
            try:
                datetime.strptime(birth_date, '%Y-%m-%d')
            except ValueError:
                messagebox.showerror("خطأ", "تنسيق التاريخ غير صحيح (YYYY-MM-DD)")
                return
        
        # إعداد بيانات الطالب
        student_data = {
            'full_name': self.full_name_var.get().strip(),
            'national_id': national_id,
            'section': self.section_var.get(),
            'birth_date': birth_date if birth_date else None,
            'gender': self.gender_var.get() if self.gender_var.get() else None,
            'phone': self.phone_var.get().strip() if self.phone_var.get().strip() else None
        }
        
        try:
            student_id, seat_number = self.db_manager.add_student(student_data)
            messagebox.showinfo(
                "نجح الحفظ", 
                f"تم حفظ بيانات الطالب بنجاح\nرقم الجلوس: {seat_number}"
            )
            
            # مسح النموذج
            self.clear_form()
            
            # تحديث قائمة الطلاب
            self.refresh_students_list()
            
        except Exception as e:
            messagebox.showerror("خطأ", str(e))
    
    def clear_form(self):
        """مسح بيانات النموذج"""
        self.full_name_var.set('')
        self.national_id_var.set('')
        self.birth_date_var.set('')
        self.gender_var.set('')
        self.phone_var.set('')
        
        # التركيز على حقل الاسم
        self.name_entry.focus()

    def load_student_data(self):
        """تحميل بيانات الطالب للتحرير"""
        if not self.student_data:
            return

        try:
            # تحميل البيانات في الحقول
            self.full_name_var.set(self.student_data.get('full_name', ''))
            self.national_id_var.set(self.student_data.get('national_id', ''))
            self.birth_date_var.set(self.student_data.get('birth_date', ''))
            self.gender_var.set(self.student_data.get('gender', ''))
            self.phone_var.set(self.student_data.get('phone', ''))
            self.section_var.set(self.student_data.get('section', ''))

            # تغيير نص الزر
            if hasattr(self, 'save_btn'):
                self.save_btn.configure(text="تحديث البيانات")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل بيانات الطالب: {str(e)}")

    def add_new_section(self):
        """إضافة شعبة جديدة"""
        dialog = tk.Toplevel(self.window)
        dialog.title("إضافة شعبة جديدة")
        dialog.geometry("400x200")
        dialog.configure(bg='#ecf0f1')
        dialog.transient(self.window)
        dialog.grab_set()
        
        # توسيط النافذة
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (200)
        y = (dialog.winfo_screenheight() // 2) - (100)
        dialog.geometry(f'400x200+{x}+{y}')
        
        # العنوان
        tk.Label(
            dialog,
            text="إضافة شعبة جديدة",
            font=get_arabic_font(size=16, weight='bold'),
            bg='#ecf0f1',
            fg='#2c3e50'
        ).pack(pady=20)
        
        # حقل الإدخال
        tk.Label(
            dialog,
            text="اسم الشعبة:",
            font=get_arabic_font(size=12),
            bg='#ecf0f1'
        ).pack(pady=(0, 10))
        
        section_name_var = tk.StringVar()
        section_entry = RTLEntry(
            dialog,
            textvariable=section_name_var,
            font=get_arabic_font(size=12),
            width=30
        )
        section_entry.pack(pady=(0, 20))
        section_entry.focus()
        
        # الأزرار
        buttons_frame = tk.Frame(dialog, bg='#ecf0f1')
        buttons_frame.pack()
        
        def save_section():
            section_name = section_name_var.get().strip()
            if not section_name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم الشعبة")
                return
            
            try:
                self.db_manager.add_section(section_name)
                messagebox.showinfo("نجح", "تم إضافة الشعبة بنجاح")
                
                # تحديث القوائم
                self.load_sections()
                self.load_display_sections()
                
                dialog.destroy()
                
            except Exception as e:
                messagebox.showerror("خطأ", str(e))
        
        create_arabic_button(
            buttons_frame,
            "حفظ",
            command=save_section,
            bg='#27ae60',
            fg='white',
            width=10
        ).pack(side='right', padx=(0, 10))
        
        create_arabic_button(
            buttons_frame,
            "إلغاء",
            command=dialog.destroy,
            bg='#e74c3c',
            fg='white',
            width=10
        ).pack(side='right')
    
    def refresh_students_list(self):
        """تحديث قائمة الطلاب"""
        self.load_display_sections()
