#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الطلاب - الملف الرئيسي
Student Management System - Main File
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# إضافة مسار المكتبات
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from database.db_manager import DatabaseManager
    from gui.main_window import MainWindow
    from utils.arabic_support import setup_arabic_font
except ImportError as e:
    print(f"خطأ في استيراد المكتبات: {e}")
    sys.exit(1)

class StudentManagementApp:
    def __init__(self):
        self.root = tk.Tk()
        self.db_manager = None
        self.main_window = None
        self.setup_app()
    
    def setup_app(self):
        """إعداد التطبيق الرئيسي"""
        try:
            # إعداد النافذة الرئيسية
            self.root.title("نظام إدارة الطلاب - معهد المتوسط للمهن الشاملة")
            self.root.geometry("1200x800")
            self.root.configure(bg='#ecf0f1')
            
            # توسيط النافذة
            self.center_window()
            
            # إعداد الخط العربي
            setup_arabic_font(self.root)
            
            # إعداد قاعدة البيانات
            self.setup_database()
            
            # إنشاء النافذة الرئيسية
            self.main_window = MainWindow(self.root, self.db_manager)
            
            # ربط إغلاق النافذة
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            
        except Exception as e:
            messagebox.showerror("خطأ في التهيئة", f"خطأ في إعداد التطبيق: {str(e)}")
            sys.exit(1)
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = 1200
        height = 800
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            # إنشاء مجلد البيانات إذا لم يكن موجوداً
            data_dir = "data"
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
            
            # مسار قاعدة البيانات
            db_path = os.path.join(data_dir, "student_data.db")
            
            # إنشاء مدير قاعدة البيانات
            self.db_manager = DatabaseManager(db_path)
            
            print("تم إعداد قاعدة البيانات بنجاح")
            
        except Exception as e:
            messagebox.showerror("خطأ في قاعدة البيانات", f"خطأ في إعداد قاعدة البيانات: {str(e)}")
            raise
    
    def on_closing(self):
        """معالجة إغلاق التطبيق"""
        try:
            # يمكن إضافة عمليات تنظيف هنا
            self.root.destroy()
        except Exception as e:
            print(f"خطأ في إغلاق التطبيق: {e}")
            self.root.destroy()
    
    def run(self):
        """تشغيل التطبيق"""
        try:
            print("بدء تشغيل نظام إدارة الطلاب...")
            self.root.mainloop()
        except Exception as e:
            messagebox.showerror("خطأ في التشغيل", f"خطأ في تشغيل التطبيق: {str(e)}")
        finally:
            print("تم إنهاء التطبيق")

def main():
    """الدالة الرئيسية"""
    try:
        # التحقق من إصدار Python
        if sys.version_info < (3, 6):
            print("يتطلب هذا البرنامج Python 3.6 أو أحدث")
            sys.exit(1)
        
        # إنشاء وتشغيل التطبيق
        app = StudentManagementApp()
        app.run()
        
    except KeyboardInterrupt:
        print("\nتم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"خطأ عام في البرنامج: {e}")
        messagebox.showerror("خطأ", f"خطأ عام في البرنامج: {str(e)}")

if __name__ == "__main__":
    main()
