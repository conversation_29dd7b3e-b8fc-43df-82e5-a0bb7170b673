#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة نقطة البيع (POS) - نظام محاسبة مكتبة التميز
Point of Sale Window for Excellence Bookstore System

المنظومة الإلكترونية - إعد<PERSON> محمد مطرود
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date
import sys
import os

# إضافة مسار المكتبات
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, current_dir)

from utils.arabic_support import *

class POSWindow:
    """نافذة نقطة البيع المتطورة"""
    
    def __init__(self, parent, db_manager, config):
        """تهيئة نافذة نقطة البيع"""
        self.parent = parent
        self.db_manager = db_manager
        self.config = config
        
        # متغيرات الفاتورة
        self.invoice_items = []
        self.current_total = 0.0
        self.current_discount = 0.0
        self.current_paid = 0.0
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.setup_ui()
        
        # تحميل المنتجات
        self.load_products()
        
        # إنشاء رقم فاتورة جديد
        self.generate_new_invoice_number()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("نقطة البيع (POS) - مكتبة التميز")
        self.window.geometry("1600x1000")
        self.window.configure(bg='#f8f9fa')
        self.window.resizable(True, True)
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # توسيط النافذة
        center_window(self.window, 1600, 1000)
        
        # ربط إغلاق النافذة
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # رأس النافذة
        self.create_header()
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.window, bg='#f8f9fa')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # تقسيم النافذة إلى ثلاثة أقسام
        left_frame = tk.Frame(main_frame, bg='#f8f9fa')
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        center_frame = tk.Frame(main_frame, bg='#f8f9fa')
        center_frame.pack(side='left', fill='both', expand=True, padx=5)
        
        right_frame = tk.Frame(main_frame, bg='#f8f9fa')
        right_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        # إنشاء الأقسام
        self.create_products_section(left_frame)
        self.create_invoice_section(center_frame)
        self.create_payment_section(right_frame)
    
    def create_header(self):
        """إنشاء رأس النافذة"""
        header_frame = tk.Frame(self.window, bg='#3498db', height=80)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        # العنوان الرئيسي
        title_label = tk.Label(
            header_frame,
            text="🛒 نقطة البيع (POS)",
            font=get_arabic_font(size=24, weight='bold'),
            bg='#3498db',
            fg='white'
        )
        title_label.pack(side='right', padx=20, pady=20)
        
        # التاريخ والوقت
        datetime_label = tk.Label(
            header_frame,
            text=datetime.now().strftime("%Y-%m-%d %H:%M"),
            font=get_arabic_font(size=14),
            bg='#3498db',
            fg='#ecf0f1'
        )
        datetime_label.pack(side='left', padx=20, pady=20)
    
    def create_products_section(self, parent):
        """إنشاء قسم المنتجات"""
        products_frame = tk.LabelFrame(
            parent,
            text="الأصناف والمنتجات",
            font=get_arabic_font(size=14, weight='bold'),
            bg='#f8f9fa',
            fg='#2c3e50',
            padx=10,
            pady=10
        )
        products_frame.pack(fill='both', expand=True)
        
        # إطار البحث
        search_frame = tk.Frame(products_frame, bg='#f8f9fa')
        search_frame.pack(fill='x', pady=(0, 10))
        
        # حقل البحث
        search_label = create_arabic_label(search_frame, "بحث في الأصناف:")
        search_label.pack(side='right', padx=(0, 10))
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        
        search_entry = RTLEntry(
            search_frame,
            textvariable=self.search_var,
            width=30,
            font=get_arabic_font(size=12)
        )
        search_entry.pack(side='right', padx=(0, 10))
        
        # زر إضافة صنف جديد
        add_product_btn = create_styled_button(
            search_frame,
            "➕ صنف جديد",
            command=self.add_new_product,
            bg='#27ae60',
            fg='white'
        )
        add_product_btn.pack(side='left', padx=5)
        
        # قائمة المنتجات
        list_container = tk.Frame(products_frame, bg='#f8f9fa')
        list_container.pack(fill='both', expand=True)
        
        # إنشاء Treeview للمنتجات
        columns = ('name', 'category', 'price', 'variable')
        self.products_tree = ttk.Treeview(
            list_container,
            columns=columns,
            show='headings',
            height=20
        )
        
        # تعيين عناوين الأعمدة
        self.products_tree.heading('name', text='اسم الصنف')
        self.products_tree.heading('category', text='الفئة')
        self.products_tree.heading('price', text='السعر')
        self.products_tree.heading('variable', text='سعر متغير')
        
        # تعيين عرض الأعمدة
        self.products_tree.column('name', width=200)
        self.products_tree.column('category', width=100)
        self.products_tree.column('price', width=80)
        self.products_tree.column('variable', width=80)
        
        # شريط التمرير
        products_scrollbar = ttk.Scrollbar(
            list_container,
            orient='vertical',
            command=self.products_tree.yview
        )
        self.products_tree.configure(yscrollcommand=products_scrollbar.set)
        
        # تخطيط العناصر
        self.products_tree.pack(side='left', fill='both', expand=True)
        products_scrollbar.pack(side='right', fill='y')
        
        # ربط الأحداث
        self.products_tree.bind('<Double-1>', self.on_product_select)
    
    def create_invoice_section(self, parent):
        """إنشاء قسم الفاتورة"""
        invoice_frame = tk.LabelFrame(
            parent,
            text="تفاصيل الفاتورة",
            font=get_arabic_font(size=14, weight='bold'),
            bg='#f8f9fa',
            fg='#2c3e50',
            padx=10,
            pady=10
        )
        invoice_frame.pack(fill='both', expand=True)
        
        # معلومات الفاتورة
        info_frame = tk.Frame(invoice_frame, bg='#f8f9fa')
        info_frame.pack(fill='x', pady=(0, 10))
        
        # رقم الفاتورة
        invoice_num_frame = tk.Frame(info_frame, bg='#f8f9fa')
        invoice_num_frame.pack(side='right', padx=10)
        
        create_arabic_label(invoice_num_frame, "رقم الفاتورة:").pack(side='right')
        self.invoice_number_var = tk.StringVar()
        invoice_num_label = tk.Label(
            invoice_num_frame,
            textvariable=self.invoice_number_var,
            font=get_arabic_font(size=12, weight='bold'),
            bg='#f8f9fa',
            fg='#e74c3c'
        )
        invoice_num_label.pack(side='right', padx=(10, 0))
        
        # التاريخ
        date_frame = tk.Frame(info_frame, bg='#f8f9fa')
        date_frame.pack(side='left', padx=10)
        
        create_arabic_label(date_frame, "التاريخ:").pack(side='right')
        self.invoice_date_var = tk.StringVar(value=date.today().strftime('%Y-%m-%d'))
        date_label = tk.Label(
            date_frame,
            textvariable=self.invoice_date_var,
            font=get_arabic_font(size=12),
            bg='#f8f9fa',
            fg='#2c3e50'
        )
        date_label.pack(side='right', padx=(10, 0))
        
        # معلومات العميل
        customer_frame = tk.Frame(invoice_frame, bg='#f8f9fa')
        customer_frame.pack(fill='x', pady=(0, 10))
        
        # اسم العميل
        customer_name_frame = tk.Frame(customer_frame, bg='#f8f9fa')
        customer_name_frame.pack(side='right', padx=5)
        
        create_arabic_label(customer_name_frame, "اسم العميل:").pack(side='right')
        self.customer_name_var = tk.StringVar(value="عميل نقدي")
        customer_name_entry = RTLEntry(
            customer_name_frame,
            textvariable=self.customer_name_var,
            width=20,
            font=get_arabic_font(size=11)
        )
        customer_name_entry.pack(side='right', padx=(10, 0))
        
        # رقم الهاتف
        phone_frame = tk.Frame(customer_frame, bg='#f8f9fa')
        phone_frame.pack(side='left', padx=5)
        
        create_arabic_label(phone_frame, "رقم الهاتف:").pack(side='right')
        self.customer_phone_var = tk.StringVar()
        phone_entry = RTLEntry(
            phone_frame,
            textvariable=self.customer_phone_var,
            width=15,
            font=get_arabic_font(size=11)
        )
        phone_entry.pack(side='right', padx=(10, 0))
        
        # البريد الإلكتروني
        email_frame = tk.Frame(invoice_frame, bg='#f8f9fa')
        email_frame.pack(fill='x', pady=(0, 10))
        
        create_arabic_label(email_frame, "البريد الإلكتروني:").pack(side='right', padx=(0, 10))
        self.customer_email_var = tk.StringVar()
        email_entry = RTLEntry(
            email_frame,
            textvariable=self.customer_email_var,
            width=30,
            font=get_arabic_font(size=11)
        )
        email_entry.pack(side='right')
        
        # جدول عناصر الفاتورة
        items_container = tk.Frame(invoice_frame, bg='#f8f9fa')
        items_container.pack(fill='both', expand=True, pady=(0, 10))
        
        # إنشاء Treeview لعناصر الفاتورة
        items_columns = ('item', 'quantity', 'price', 'total')
        self.items_tree = ttk.Treeview(
            items_container,
            columns=items_columns,
            show='headings',
            height=15
        )
        
        # تعيين عناوين الأعمدة
        self.items_tree.heading('item', text='الصنف')
        self.items_tree.heading('quantity', text='الكمية')
        self.items_tree.heading('price', text='السعر')
        self.items_tree.heading('total', text='الإجمالي')
        
        # تعيين عرض الأعمدة
        self.items_tree.column('item', width=200)
        self.items_tree.column('quantity', width=80)
        self.items_tree.column('price', width=80)
        self.items_tree.column('total', width=100)
        
        # شريط التمرير
        items_scrollbar = ttk.Scrollbar(
            items_container,
            orient='vertical',
            command=self.items_tree.yview
        )
        self.items_tree.configure(yscrollcommand=items_scrollbar.set)
        
        # تخطيط العناصر
        self.items_tree.pack(side='left', fill='both', expand=True)
        items_scrollbar.pack(side='right', fill='y')
        
        # ربط الأحداث
        self.items_tree.bind('<Delete>', self.remove_item)
        self.items_tree.bind('<Double-1>', self.edit_item)
        
        # أزرار التحكم في العناصر
        items_buttons_frame = tk.Frame(invoice_frame, bg='#f8f9fa')
        items_buttons_frame.pack(fill='x', pady=(0, 10))
        
        remove_item_btn = create_styled_button(
            items_buttons_frame,
            "🗑️ حذف العنصر",
            command=self.remove_selected_item,
            bg='#e74c3c',
            fg='white'
        )
        remove_item_btn.pack(side='left', padx=5)
        
        edit_item_btn = create_styled_button(
            items_buttons_frame,
            "✏️ تعديل العنصر",
            command=self.edit_selected_item,
            bg='#f39c12',
            fg='white'
        )
        edit_item_btn.pack(side='left', padx=5)
        
        clear_all_btn = create_styled_button(
            items_buttons_frame,
            "🗑️ مسح الكل",
            command=self.clear_all_items,
            bg='#95a5a6',
            fg='white'
        )
        clear_all_btn.pack(side='right', padx=5)
    
    def create_payment_section(self, parent):
        """إنشاء قسم الدفع"""
        payment_frame = tk.LabelFrame(
            parent,
            text="الدفع والإجماليات",
            font=get_arabic_font(size=14, weight='bold'),
            bg='#f8f9fa',
            fg='#2c3e50',
            padx=10,
            pady=10
        )
        payment_frame.pack(fill='both', expand=True)
        
        # الإجماليات
        totals_frame = tk.Frame(payment_frame, bg='#f8f9fa')
        totals_frame.pack(fill='x', pady=(0, 20))
        
        # المجموع الفرعي
        subtotal_frame = tk.Frame(totals_frame, bg='#f8f9fa')
        subtotal_frame.pack(fill='x', pady=5)
        
        create_arabic_label(
            subtotal_frame, 
            "المجموع الفرعي:",
            font=get_arabic_font(size=12)
        ).pack(side='right')
        
        self.subtotal_var = tk.StringVar(value="0.00 ريال")
        subtotal_label = tk.Label(
            subtotal_frame,
            textvariable=self.subtotal_var,
            font=get_arabic_font(size=12, weight='bold'),
            bg='#f8f9fa',
            fg='#2c3e50'
        )
        subtotal_label.pack(side='left')
        
        # الخصم
        discount_frame = tk.Frame(totals_frame, bg='#f8f9fa')
        discount_frame.pack(fill='x', pady=5)
        
        create_arabic_label(
            discount_frame, 
            "الخصم:",
            font=get_arabic_font(size=12)
        ).pack(side='right')
        
        self.discount_var = tk.StringVar(value="0")
        discount_entry = RTLEntry(
            discount_frame,
            textvariable=self.discount_var,
            width=10,
            font=get_arabic_font(size=11)
        )
        discount_entry.pack(side='right', padx=(10, 0))
        discount_entry.bind('<KeyRelease>', self.calculate_totals)
        
        create_arabic_label(discount_frame, "ريال").pack(side='right', padx=(5, 0))
        
        # المجموع النهائي
        total_frame = tk.Frame(totals_frame, bg='#f8f9fa')
        total_frame.pack(fill='x', pady=10)
        
        create_arabic_label(
            total_frame, 
            "المجموع النهائي:",
            font=get_arabic_font(size=14, weight='bold')
        ).pack(side='right')
        
        self.total_var = tk.StringVar(value="0.00 ريال")
        total_label = tk.Label(
            total_frame,
            textvariable=self.total_var,
            font=get_arabic_font(size=16, weight='bold'),
            bg='#f8f9fa',
            fg='#e74c3c'
        )
        total_label.pack(side='left')
        
        # طريقة الدفع
        payment_method_frame = tk.Frame(payment_frame, bg='#f8f9fa')
        payment_method_frame.pack(fill='x', pady=(0, 15))
        
        create_arabic_label(
            payment_method_frame,
            "طريقة الدفع:",
            font=get_arabic_font(size=12, weight='bold')
        ).pack(side='right', padx=(0, 10))
        
        self.payment_method_var = tk.StringVar(value="نقدي")
        payment_methods = ["نقدي", "بطاقة", "آجلة"]
        
        for method in payment_methods:
            radio_btn = tk.Radiobutton(
                payment_method_frame,
                text=method,
                variable=self.payment_method_var,
                value=method,
                font=get_arabic_font(size=11),
                bg='#f8f9fa'
            )
            radio_btn.pack(side='right', padx=10)
        
        # المبلغ المدفوع
        paid_frame = tk.Frame(payment_frame, bg='#f8f9fa')
        paid_frame.pack(fill='x', pady=(0, 15))
        
        create_arabic_label(
            paid_frame,
            "المبلغ المدفوع:",
            font=get_arabic_font(size=12, weight='bold')
        ).pack(side='right', padx=(0, 10))
        
        self.paid_amount_var = tk.StringVar(value="0")
        paid_entry = RTLEntry(
            paid_frame,
            textvariable=self.paid_amount_var,
            width=15,
            font=get_arabic_font(size=12)
        )
        paid_entry.pack(side='right', padx=(0, 5))
        paid_entry.bind('<KeyRelease>', self.calculate_remaining)
        
        create_arabic_label(paid_frame, "ريال").pack(side='right')
        
        # المبلغ المتبقي
        remaining_frame = tk.Frame(payment_frame, bg='#f8f9fa')
        remaining_frame.pack(fill='x', pady=(0, 20))
        
        create_arabic_label(
            remaining_frame,
            "المبلغ المتبقي:",
            font=get_arabic_font(size=12, weight='bold')
        ).pack(side='right', padx=(0, 10))
        
        self.remaining_var = tk.StringVar(value="0.00 ريال")
        remaining_label = tk.Label(
            remaining_frame,
            textvariable=self.remaining_var,
            font=get_arabic_font(size=12, weight='bold'),
            bg='#f8f9fa',
            fg='#e74c3c'
        )
        remaining_label.pack(side='left')
        
        # أزرار العمليات
        buttons_frame = tk.Frame(payment_frame, bg='#f8f9fa')
        buttons_frame.pack(fill='x', pady=(0, 10))
        
        # زر حفظ الفاتورة
        save_btn = create_styled_button(
            buttons_frame,
            "💾 حفظ الفاتورة",
            command=self.save_invoice,
            bg='#27ae60',
            fg='white',
            width=15
        )
        save_btn.pack(fill='x', pady=5)
        
        # زر طباعة
        print_btn = create_styled_button(
            buttons_frame,
            "🖨️ طباعة",
            command=self.print_invoice,
            bg='#3498db',
            fg='white',
            width=15
        )
        print_btn.pack(fill='x', pady=5)
        
        # زر فاتورة جديدة
        new_btn = create_styled_button(
            buttons_frame,
            "📄 فاتورة جديدة",
            command=self.new_invoice,
            bg='#f39c12',
            fg='white',
            width=15
        )
        new_btn.pack(fill='x', pady=5)
        
        # زر إغلاق
        close_btn = create_styled_button(
            buttons_frame,
            "❌ إغلاق",
            command=self.on_closing,
            bg='#95a5a6',
            fg='white',
            width=15
        )
        close_btn.pack(fill='x', pady=5)

    def load_products(self):
        """تحميل المنتجات"""
        try:
            # مسح القائمة الحالية
            for item in self.products_tree.get_children():
                self.products_tree.delete(item)

            # الحصول على جميع المنتجات
            products = self.db_manager.get_all_products()

            # إضافة المنتجات إلى القائمة
            for product in products:
                variable_text = "نعم" if product[4] else "لا"  # is_variable_price
                price_text = f"{product[3]:.2f}" if product[3] > 0 else "متغير"

                self.products_tree.insert('', 'end', values=(
                    product[1],  # name
                    product[2],  # category
                    price_text,  # price
                    variable_text  # variable
                ), tags=(product[0],))  # product_id في tags

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل المنتجات: {str(e)}")

    def on_search_change(self, *args):
        """البحث في المنتجات"""
        search_term = self.search_var.get().strip()

        if not search_term:
            self.load_products()
            return

        try:
            # البحث في قاعدة البيانات
            results = self.db_manager.search_products(search_term)

            # مسح القائمة الحالية
            for item in self.products_tree.get_children():
                self.products_tree.delete(item)

            # إضافة نتائج البحث
            for product in results:
                variable_text = "نعم" if product[4] else "لا"
                price_text = f"{product[3]:.2f}" if product[3] > 0 else "متغير"

                self.products_tree.insert('', 'end', values=(
                    product[1],  # name
                    product[2],  # category
                    price_text,  # price
                    variable_text  # variable
                ), tags=(product[0],))

        except Exception as e:
            print(f"خطأ في البحث: {e}")

    def on_product_select(self, event):
        """عند اختيار منتج من القائمة"""
        selection = self.products_tree.selection()
        if not selection:
            return

        # الحصول على معرف المنتج
        item = self.products_tree.item(selection[0])
        product_id = item['tags'][0] if item['tags'] else None

        if product_id:
            self.add_product_to_invoice(product_id)

    def add_product_to_invoice(self, product_id):
        """إضافة منتج إلى الفاتورة"""
        try:
            # الحصول على بيانات المنتج
            products = self.db_manager.get_all_products()
            product = None
            for p in products:
                if p[0] == product_id:
                    product = p
                    break

            if not product:
                messagebox.showerror("خطأ", "المنتج غير موجود")
                return

            # فتح نافذة إدخال التفاصيل
            self.open_item_dialog(product)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة المنتج: {str(e)}")

    def open_item_dialog(self, product=None):
        """فتح نافذة إدخال تفاصيل العنصر"""
        dialog = tk.Toplevel(self.window)
        dialog.title("إضافة عنصر للفاتورة")
        dialog.geometry("400x300")
        dialog.configure(bg='#f8f9fa')
        dialog.transient(self.window)
        dialog.grab_set()

        center_window(dialog, 400, 300)

        # متغيرات النافذة
        item_name_var = tk.StringVar(value=product[1] if product else "")
        quantity_var = tk.StringVar(value="1")
        price_var = tk.StringVar(value=str(product[3]) if product and product[3] > 0 else "0")

        # العنوان
        title_label = create_arabic_label(
            dialog,
            "تفاصيل العنصر",
            font=get_arabic_font(size=16, weight='bold')
        )
        title_label.pack(pady=20)

        # اسم العنصر
        name_frame = tk.Frame(dialog, bg='#f8f9fa')
        name_frame.pack(fill='x', padx=20, pady=10)

        create_arabic_label(name_frame, "اسم العنصر:").pack(side='right')
        name_entry = RTLEntry(
            name_frame,
            textvariable=item_name_var,
            width=25,
            font=get_arabic_font(size=11)
        )
        name_entry.pack(side='right', padx=(10, 0))

        # الكمية
        quantity_frame = tk.Frame(dialog, bg='#f8f9fa')
        quantity_frame.pack(fill='x', padx=20, pady=10)

        create_arabic_label(quantity_frame, "الكمية:").pack(side='right')
        quantity_entry = RTLEntry(
            quantity_frame,
            textvariable=quantity_var,
            width=10,
            font=get_arabic_font(size=11)
        )
        quantity_entry.pack(side='right', padx=(10, 0))

        # السعر
        price_frame = tk.Frame(dialog, bg='#f8f9fa')
        price_frame.pack(fill='x', padx=20, pady=10)

        create_arabic_label(price_frame, "السعر:").pack(side='right')
        price_entry = RTLEntry(
            price_frame,
            textvariable=price_var,
            width=15,
            font=get_arabic_font(size=11)
        )
        price_entry.pack(side='right', padx=(10, 0))
        create_arabic_label(price_frame, "ريال").pack(side='right', padx=(5, 0))

        # الإجمالي (للعرض فقط)
        total_frame = tk.Frame(dialog, bg='#f8f9fa')
        total_frame.pack(fill='x', padx=20, pady=10)

        create_arabic_label(total_frame, "الإجمالي:").pack(side='right')
        total_var = tk.StringVar(value="0.00 ريال")
        total_label = tk.Label(
            total_frame,
            textvariable=total_var,
            font=get_arabic_font(size=11, weight='bold'),
            bg='#f8f9fa',
            fg='#e74c3c'
        )
        total_label.pack(side='right', padx=(10, 0))

        # دالة حساب الإجمالي
        def calculate_item_total(*args):
            try:
                quantity = float(quantity_var.get() or 0)
                price = float(price_var.get() or 0)
                total = quantity * price
                total_var.set(f"{total:.2f} ريال")
            except:
                total_var.set("0.00 ريال")

        # ربط الحساب التلقائي
        quantity_var.trace('w', calculate_item_total)
        price_var.trace('w', calculate_item_total)
        calculate_item_total()

        # الأزرار
        buttons_frame = tk.Frame(dialog, bg='#f8f9fa')
        buttons_frame.pack(fill='x', padx=20, pady=20)

        def add_item():
            try:
                name = item_name_var.get().strip()
                quantity = float(quantity_var.get() or 0)
                price = float(price_var.get() or 0)

                if not name:
                    messagebox.showerror("خطأ", "يرجى إدخال اسم العنصر")
                    return

                if quantity <= 0:
                    messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة")
                    return

                if price <= 0:
                    messagebox.showerror("خطأ", "يرجى إدخال سعر صحيح")
                    return

                # إضافة العنصر للفاتورة
                item_data = {
                    'product_id': product[0] if product else None,
                    'name': name,
                    'quantity': quantity,
                    'price': price,
                    'total': quantity * price
                }

                self.invoice_items.append(item_data)
                self.update_invoice_display()
                dialog.destroy()

            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة للكمية والسعر")
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في إضافة العنصر: {str(e)}")

        add_btn = create_styled_button(
            buttons_frame,
            "إضافة",
            command=add_item,
            bg='#27ae60',
            fg='white'
        )
        add_btn.pack(side='left', padx=5)

        cancel_btn = create_styled_button(
            buttons_frame,
            "إلغاء",
            command=dialog.destroy,
            bg='#95a5a6',
            fg='white'
        )
        cancel_btn.pack(side='right', padx=5)

    def update_invoice_display(self):
        """تحديث عرض الفاتورة"""
        # مسح العناصر الحالية
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # إضافة العناصر الجديدة
        for item in self.invoice_items:
            self.items_tree.insert('', 'end', values=(
                item['name'],
                f"{item['quantity']:.2f}",
                f"{item['price']:.2f}",
                f"{item['total']:.2f}"
            ))

        # حساب الإجماليات
        self.calculate_totals()

    def calculate_totals(self, *args):
        """حساب الإجماليات"""
        try:
            # حساب المجموع الفرعي
            subtotal = sum(item['total'] for item in self.invoice_items)

            # حساب الخصم
            discount = float(self.discount_var.get() or 0)

            # حساب المجموع النهائي
            total = subtotal - discount
            if total < 0:
                total = 0

            # تحديث العرض
            self.subtotal_var.set(f"{subtotal:.2f} ريال")
            self.total_var.set(f"{total:.2f} ريال")

            # حفظ القيم
            self.current_total = total
            self.current_discount = discount

            # حساب المبلغ المتبقي
            self.calculate_remaining()

        except Exception as e:
            print(f"خطأ في حساب الإجماليات: {e}")

    def calculate_remaining(self, *args):
        """حساب المبلغ المتبقي"""
        try:
            paid = float(self.paid_amount_var.get() or 0)
            remaining = self.current_total - paid

            if remaining < 0:
                remaining = 0

            self.remaining_var.set(f"{remaining:.2f} ريال")
            self.current_paid = paid

        except Exception as e:
            print(f"خطأ في حساب المبلغ المتبقي: {e}")

    def remove_selected_item(self):
        """حذف العنصر المحدد"""
        selection = self.items_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار عنصر للحذف")
            return

        # الحصول على فهرس العنصر
        item_index = self.items_tree.index(selection[0])

        # حذف العنصر
        if 0 <= item_index < len(self.invoice_items):
            del self.invoice_items[item_index]
            self.update_invoice_display()

    def edit_selected_item(self):
        """تعديل العنصر المحدد"""
        selection = self.items_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار عنصر للتعديل")
            return

        # الحصول على فهرس العنصر
        item_index = self.items_tree.index(selection[0])

        if 0 <= item_index < len(self.invoice_items):
            item = self.invoice_items[item_index]
            # فتح نافذة التعديل (يمكن تطويرها لاحقاً)
            messagebox.showinfo("قريباً", "سيتم إضافة نافذة تعديل العنصر قريباً")

    def clear_all_items(self):
        """مسح جميع العناصر"""
        if self.invoice_items:
            if messagebox.askyesno("تأكيد", "هل تريد مسح جميع عناصر الفاتورة؟"):
                self.invoice_items.clear()
                self.update_invoice_display()

    def add_new_product(self):
        """إضافة منتج جديد"""
        # فتح نافذة إضافة منتج (بدون منتج محدد)
        self.open_item_dialog()

    def generate_new_invoice_number(self):
        """توليد رقم فاتورة جديد"""
        try:
            invoice_number = self.db_manager.get_next_invoice_number()
            self.invoice_number_var.set(invoice_number)
        except Exception as e:
            print(f"خطأ في توليد رقم الفاتورة: {e}")
            self.invoice_number_var.set("INV000001")

    def save_invoice(self):
        """حفظ الفاتورة"""
        try:
            # التحقق من وجود عناصر
            if not self.invoice_items:
                messagebox.showwarning("تحذير", "لا يمكن حفظ فاتورة فارغة")
                return

            # التحقق من المبلغ المدفوع
            if self.current_paid > self.current_total:
                messagebox.showwarning("تحذير", "المبلغ المدفوع أكبر من المجموع النهائي")
                return

            # تحديد حالة الفاتورة
            remaining = self.current_total - self.current_paid
            if remaining > 0:
                status = "مديونية"
            else:
                status = "مكتملة"

            # بيانات الفاتورة
            invoice_data = {
                'invoice_number': self.invoice_number_var.get(),
                'date': self.invoice_date_var.get(),
                'customer_name': self.customer_name_var.get() or "عميل نقدي",
                'customer_email': self.customer_email_var.get(),
                'customer_phone': self.customer_phone_var.get(),
                'subtotal': sum(item['total'] for item in self.invoice_items),
                'discount': self.current_discount,
                'total': self.current_total,
                'payment_method': self.payment_method_var.get(),
                'paid_amount': self.current_paid,
                'remaining_amount': remaining,
                'status': status
            }

            # بيانات العناصر
            items_data = []
            for item in self.invoice_items:
                items_data.append({
                    'product_id': item.get('product_id'),
                    'product_name': item['name'],
                    'quantity': item['quantity'],
                    'unit_price': item['price'],
                    'total_price': item['total']
                })

            # حفظ الفاتورة
            invoice_id = self.db_manager.create_invoice(invoice_data, items_data)

            messagebox.showinfo(
                "نجح الحفظ",
                f"تم حفظ الفاتورة بنجاح\nرقم الفاتورة: {invoice_data['invoice_number']}"
            )

            # إعادة تعيين النافذة
            self.new_invoice()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الفاتورة: {str(e)}")

    def print_invoice(self):
        """طباعة الفاتورة"""
        if not self.invoice_items:
            messagebox.showwarning("تحذير", "لا يمكن طباعة فاتورة فارغة")
            return

        messagebox.showinfo("قريباً", "سيتم إضافة وظيفة الطباعة قريباً")

    def new_invoice(self):
        """فاتورة جديدة"""
        # مسح البيانات الحالية
        self.invoice_items.clear()
        self.current_total = 0.0
        self.current_discount = 0.0
        self.current_paid = 0.0

        # إعادة تعيين الحقول
        self.customer_name_var.set("عميل نقدي")
        self.customer_email_var.set("")
        self.customer_phone_var.set("")
        self.discount_var.set("0")
        self.paid_amount_var.set("0")
        self.payment_method_var.set("نقدي")

        # توليد رقم فاتورة جديد
        self.generate_new_invoice_number()

        # تحديث التاريخ
        self.invoice_date_var.set(date.today().strftime('%Y-%m-%d'))

        # تحديث العرض
        self.update_invoice_display()

    def on_closing(self):
        """معالجة إغلاق النافذة"""
        if self.invoice_items:
            if messagebox.askyesno("تأكيد الإغلاق", "هناك فاتورة غير محفوظة. هل تريد الإغلاق؟"):
                self.window.destroy()
        else:
            self.window.destroy()
