#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النافذة الرئيسية - نظام محاسبة مكتبة التميز
Main Window for Excellence Bookstore System

المنظومة الإلكترونية - إعداد محمد مطرود
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date
import sys
import os

# إضافة مسار المكتبات
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, current_dir)

from utils.arabic_support import *

class MainWindow:
    """النافذة الرئيسية لنظام محاسبة مكتبة التميز"""
    
    def __init__(self, root, db_manager, config):
        """تهيئة النافذة الرئيسية"""
        self.root = root
        self.db_manager = db_manager
        self.config = config
        
        # إعداد واجهة المستخدم
        self.setup_ui()
        
        # تحديث الإحصائيات
        self.update_statistics()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم الرئيسية"""
        # إطار العنوان الرئيسي
        self.create_header()
        
        # الإطار الرئيسي للمحتوى
        main_frame = tk.Frame(self.root, bg='#f8f9fa')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # إنشاء الأقسام الرئيسية
        self.create_quick_access_panel(main_frame)
        self.create_main_buttons(main_frame)
        self.create_statistics_panel(main_frame)
        
        # شريط الحالة
        self.create_status_bar()
    
    def create_header(self):
        """إنشاء رأس النافذة"""
        header_frame = tk.Frame(self.root, bg='#2c3e50', height=120)
        header_frame.pack(fill='x', pady=(0, 10))
        header_frame.pack_propagate(False)
        
        # العنوان الرئيسي
        title_label = tk.Label(
            header_frame,
            text=self.config.store_name,
            font=get_arabic_font(size=32, weight='bold'),
            fg='white',
            bg='#2c3e50'
        )
        title_label.pack(pady=(15, 5))
        
        # العنوان الفرعي
        subtitle_label = tk.Label(
            header_frame,
            text="نظام محاسبة متكامل",
            font=get_arabic_font(size=18),
            fg='#ecf0f1',
            bg='#2c3e50'
        )
        subtitle_label.pack()
        
        # معلومات المطور
        developer_label = tk.Label(
            header_frame,
            text=self.config.developer_info,
            font=get_arabic_font(size=12),
            fg='#bdc3c7',
            bg='#2c3e50'
        )
        developer_label.pack(pady=(5, 0))
    
    def create_quick_access_panel(self, parent):
        """إنشاء لوحة الوصول السريع"""
        quick_frame = tk.LabelFrame(
            parent,
            text="الوصول السريع",
            font=get_arabic_font(size=14, weight='bold'),
            bg='#f8f9fa',
            fg='#2c3e50',
            padx=10,
            pady=10
        )
        quick_frame.pack(fill='x', pady=(0, 15))
        
        # إطار الأزرار السريعة
        buttons_frame = tk.Frame(quick_frame, bg='#f8f9fa')
        buttons_frame.pack(fill='x')
        
        # أزرار الوصول السريع
        quick_buttons = [
            {
                'text': 'فاتورة جديدة',
                'icon': '🧾',
                'command': self.new_invoice,
                'color': '#3498db'
            },
            {
                'text': 'بحث سريع',
                'icon': '🔍',
                'command': self.quick_search,
                'color': '#f39c12'
            },
            {
                'text': 'مصروف جديد',
                'icon': '💸',
                'command': self.new_expense,
                'color': '#e74c3c'
            },
            {
                'text': 'الديون',
                'icon': '📋',
                'command': self.manage_debts,
                'color': '#9b59b6'
            },
            {
                'text': 'نسخة احتياطية',
                'icon': '💾',
                'command': self.quick_backup,
                'color': '#27ae60'
            }
        ]
        
        for i, btn_data in enumerate(quick_buttons):
            btn = create_styled_button(
                buttons_frame,
                f"{btn_data['icon']} {btn_data['text']}",
                command=btn_data['command'],
                bg=btn_data['color'],
                fg='white',
                width=18
            )
            btn.grid(row=0, column=i, padx=5, pady=5, sticky='ew')
        
        # تكوين الأعمدة للتوسع المتساوي
        for i in range(len(quick_buttons)):
            buttons_frame.columnconfigure(i, weight=1)
    
    def create_main_buttons(self, parent):
        """إنشاء الأزرار الرئيسية"""
        buttons_frame = tk.LabelFrame(
            parent,
            text="الوظائف الرئيسية",
            font=get_arabic_font(size=14, weight='bold'),
            bg='#f8f9fa',
            fg='#2c3e50',
            padx=10,
            pady=10
        )
        buttons_frame.pack(fill='both', expand=True, pady=(0, 15))
        
        # إطار الشبكة للأزرار
        grid_frame = tk.Frame(buttons_frame, bg='#f8f9fa')
        grid_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # بيانات الأزرار الرئيسية
        main_buttons = [
            {
                'text': 'نقطة البيع (POS)',
                'icon': '🛒',
                'command': self.open_pos,
                'color': '#3498db',
                'description': 'إنشاء فواتير المبيعات والدفع',
                'row': 0, 'col': 0
            },
            {
                'text': 'إدارة الأصناف',
                'icon': '📦',
                'command': self.manage_products,
                'color': '#2ecc71',
                'description': 'إضافة وتعديل المنتجات والأسعار',
                'row': 0, 'col': 1
            },
            {
                'text': 'إدارة المصاريف',
                'icon': '💰',
                'command': self.manage_expenses,
                'color': '#e74c3c',
                'description': 'تسجيل وإدارة مصاريف المكتبة',
                'row': 0, 'col': 2
            },
            {
                'text': 'إدارة الديون',
                'icon': '📊',
                'command': self.manage_debts,
                'color': '#9b59b6',
                'description': 'متابعة الديون والأقساط',
                'row': 1, 'col': 0
            },
            {
                'text': 'التقارير المالية',
                'icon': '📈',
                'command': self.financial_reports,
                'color': '#f39c12',
                'description': 'تقارير المبيعات والأرباح',
                'row': 1, 'col': 1
            },
            {
                'text': 'إعدادات النظام',
                'icon': '⚙️',
                'command': self.system_settings,
                'color': '#34495e',
                'description': 'إعدادات المكتبة والنظام',
                'row': 1, 'col': 2
            }
        ]
        
        # إنشاء الأزرار
        for btn_data in main_buttons:
            self.create_main_button(
                grid_frame,
                btn_data['text'],
                btn_data['icon'],
                btn_data['command'],
                btn_data['color'],
                btn_data['description'],
                btn_data['row'],
                btn_data['col']
            )
        
        # تكوين الشبكة للتوسع المتساوي
        for i in range(3):
            grid_frame.columnconfigure(i, weight=1)
        for i in range(2):
            grid_frame.rowconfigure(i, weight=1)
    
    def create_main_button(self, parent, text, icon, command, color, description, row, col):
        """إنشاء زر رئيسي مُنسق"""
        # إطار الزر
        button_frame = tk.Frame(
            parent,
            bg=color,
            relief='raised',
            bd=2,
            cursor='hand2'
        )
        button_frame.grid(
            row=row, column=col,
            padx=10, pady=10,
            sticky='nsew',
            ipadx=15, ipady=15
        )
        
        # ربط النقر بالإطار
        button_frame.bind('<Button-1>', lambda e: command())
        
        # الأيقونة
        icon_label = tk.Label(
            button_frame,
            text=icon,
            font=('Arial', 42),
            bg=color,
            fg='white'
        )
        icon_label.pack(pady=(20, 10))
        icon_label.bind('<Button-1>', lambda e: command())
        
        # النص الرئيسي
        text_label = tk.Label(
            button_frame,
            text=text,
            font=get_arabic_font(size=18, weight='bold'),
            bg=color,
            fg='white',
            wraplength=200
        )
        text_label.pack(pady=(0, 5))
        text_label.bind('<Button-1>', lambda e: command())
        
        # الوصف
        desc_label = tk.Label(
            button_frame,
            text=description,
            font=get_arabic_font(size=11),
            bg=color,
            fg='#ecf0f1',
            wraplength=200
        )
        desc_label.pack(pady=(0, 20))
        desc_label.bind('<Button-1>', lambda e: command())
        
        # تأثيرات التفاعل
        def on_enter(event):
            button_frame.configure(relief='sunken')
        
        def on_leave(event):
            button_frame.configure(relief='raised')
        
        button_frame.bind('<Enter>', on_enter)
        button_frame.bind('<Leave>', on_leave)
        
        # ربط جميع العناصر الفرعية بالأحداث
        for child in button_frame.winfo_children():
            child.bind('<Enter>', on_enter)
            child.bind('<Leave>', on_leave)
    
    def create_statistics_panel(self, parent):
        """إنشاء لوحة الإحصائيات"""
        stats_frame = tk.LabelFrame(
            parent,
            text="إحصائيات اليوم",
            font=get_arabic_font(size=14, weight='bold'),
            bg='#f8f9fa',
            fg='#2c3e50',
            padx=10,
            pady=10
        )
        stats_frame.pack(fill='x', pady=(0, 10))
        
        # إطار الإحصائيات
        self.stats_content = tk.Frame(stats_frame, bg='#f8f9fa')
        self.stats_content.pack(fill='x', padx=10, pady=5)
        
        # تحديث الإحصائيات
        self.update_statistics()
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = tk.Frame(self.root, bg='#34495e', height=35)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)
        
        # معلومات الحالة
        self.status_label = tk.Label(
            status_frame,
            text="جاهز",
            font=get_arabic_font(size=10),
            bg='#34495e',
            fg='white',
            anchor='w'
        )
        self.status_label.pack(side='left', padx=10, pady=8)
        
        # التاريخ والوقت
        self.time_label = tk.Label(
            status_frame,
            text="",
            font=get_arabic_font(size=10),
            bg='#34495e',
            fg='#bdc3c7',
            anchor='e'
        )
        self.time_label.pack(side='right', padx=10, pady=8)
        
        # تحديث الوقت
        self.update_time()
    
    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        
        # جدولة التحديث التالي
        self.root.after(1000, self.update_time)
    
    def update_statistics(self):
        """تحديث الإحصائيات"""
        # مسح المحتوى السابق
        for widget in self.stats_content.winfo_children():
            widget.destroy()
        
        try:
            # الحصول على إحصائيات اليوم
            today = date.today().strftime('%Y-%m-%d')
            daily_report = self.db_manager.get_daily_sales_report(today)
            
            if daily_report:
                # عرض الإحصائيات
                stats_data = [
                    ("عدد الفواتير", daily_report.get('invoices_count', 0)),
                    ("إجمالي المبيعات", self.config.format_currency(daily_report.get('total_sales', 0))),
                    ("المبلغ المحصل", self.config.format_currency(daily_report.get('total_paid', 0))),
                    ("المبلغ المتبقي", self.config.format_currency(daily_report.get('total_remaining', 0)))
                ]
            else:
                stats_data = [
                    ("عدد الفواتير", 0),
                    ("إجمالي المبيعات", self.config.format_currency(0)),
                    ("المبلغ المحصل", self.config.format_currency(0)),
                    ("المبلغ المتبقي", self.config.format_currency(0))
                ]
            
            # عرض الإحصائيات في شبكة
            for i, (label, value) in enumerate(stats_data):
                # التسمية
                label_widget = tk.Label(
                    self.stats_content,
                    text=f"{label}:",
                    font=get_arabic_font(size=12, weight='bold'),
                    bg='#f8f9fa',
                    fg='#2c3e50'
                )
                label_widget.grid(row=0, column=i*2, padx=(0, 5), pady=5, sticky='e')
                
                # القيمة
                value_widget = tk.Label(
                    self.stats_content,
                    text=str(value),
                    font=get_arabic_font(size=12),
                    bg='#f8f9fa',
                    fg='#27ae60' if 'المحصل' in label else '#34495e'
                )
                value_widget.grid(row=0, column=i*2+1, padx=(0, 20), pady=5, sticky='w')
            
        except Exception as e:
            error_label = tk.Label(
                self.stats_content,
                text="خطأ في تحميل الإحصائيات",
                font=get_arabic_font(size=12),
                bg='#f8f9fa',
                fg='#e74c3c'
            )
            error_label.pack(pady=5)
            print(f"خطأ في تحديث الإحصائيات: {e}")
    
    def update_status(self, message):
        """تحديث رسالة الحالة"""
        self.status_label.config(text=message)
        self.root.update_idletasks()
    
    # وظائف الأزرار السريعة
    def new_invoice(self):
        """إنشاء فاتورة جديدة"""
        self.update_status("فتح نافذة فاتورة جديدة...")
        self.open_pos()
    
    def quick_search(self):
        """بحث سريع"""
        self.update_status("فتح نافذة البحث السريع...")
        messagebox.showinfo("قريباً", "سيتم إضافة نافذة البحث السريع قريباً")
    
    def new_expense(self):
        """إضافة مصروف جديد"""
        self.update_status("فتح نافذة مصروف جديد...")
        self.manage_expenses()
    
    def quick_backup(self):
        """إنشاء نسخة احتياطية سريعة"""
        self.update_status("إنشاء نسخة احتياطية...")
        try:
            success, result = self.db_manager.create_backup(
                description="نسخة احتياطية سريعة"
            )
            if success:
                messagebox.showinfo("نسخة احتياطية", f"تم إنشاء النسخة الاحتياطية بنجاح\n{result}")
            else:
                messagebox.showerror("خطأ", f"فشل في إنشاء النسخة الاحتياطية\n{result}")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
        finally:
            self.update_status("جاهز")
    
    # وظائف الأزرار الرئيسية
    def open_pos(self):
        """فتح نافذة نقطة البيع"""
        self.update_status("فتح نافذة نقطة البيع...")
        try:
            from gui.pos_window import POSWindow
            POSWindow(self.root, self.db_manager, self.config)
        except ImportError:
            messagebox.showinfo("قريباً", "سيتم إضافة نافذة نقطة البيع قريباً")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة نقطة البيع: {str(e)}")
        finally:
            self.update_status("جاهز")
    
    def manage_products(self):
        """إدارة الأصناف"""
        self.update_status("فتح نافذة إدارة الأصناف...")
        try:
            from gui.products_window import ProductsWindow
            ProductsWindow(self.root, self.db_manager, self.config)
        except ImportError:
            messagebox.showinfo("قريباً", "سيتم إضافة نافذة إدارة الأصناف قريباً")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة إدارة الأصناف: {str(e)}")
        finally:
            self.update_status("جاهز")
    
    def manage_expenses(self):
        """إدارة المصاريف"""
        self.update_status("فتح نافذة إدارة المصاريف...")
        try:
            from gui.expenses_window import ExpensesWindow
            ExpensesWindow(self.root, self.db_manager, self.config)
        except ImportError:
            messagebox.showinfo("قريباً", "سيتم إضافة نافذة إدارة المصاريف قريباً")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة إدارة المصاريف: {str(e)}")
        finally:
            self.update_status("جاهز")
    
    def manage_debts(self):
        """إدارة الديون"""
        self.update_status("فتح نافذة إدارة الديون...")
        try:
            from gui.debts_window import DebtsWindow
            DebtsWindow(self.root, self.db_manager, self.config)
        except ImportError:
            messagebox.showinfo("قريباً", "سيتم إضافة نافذة إدارة الديون قريباً")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة إدارة الديون: {str(e)}")
        finally:
            self.update_status("جاهز")
    
    def financial_reports(self):
        """التقارير المالية"""
        self.update_status("فتح نافذة التقارير المالية...")
        try:
            from gui.reports_window import ReportsWindow
            ReportsWindow(self.root, self.db_manager, self.config)
        except ImportError:
            messagebox.showinfo("قريباً", "سيتم إضافة نافذة التقارير المالية قريباً")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة التقارير المالية: {str(e)}")
        finally:
            self.update_status("جاهز")
    
    def system_settings(self):
        """إعدادات النظام"""
        self.update_status("فتح نافذة إعدادات النظام...")
        try:
            from gui.settings_window import SettingsWindow
            settings_window = SettingsWindow(self.root, self.config)
            # تحديث الإحصائيات بعد إغلاق نافذة الإعدادات
            self.root.wait_window(settings_window.window)
            self.update_statistics()
        except ImportError:
            messagebox.showinfo("قريباً", "سيتم إضافة نافذة إعدادات النظام قريباً")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة إعدادات النظام: {str(e)}")
        finally:
            self.update_status("جاهز")
